#!/usr/bin/env node

/**
 * Cypress Project Scaffold CLI - Main Bootstrap Script
 *
 * This is the main entry point for the Cypress bootstrapper CLI tool.
 * It orchestrates the entire project setup process including:
 * - Environment validation
 * - User input collection
 * - Project structure creation
 * - File generation
 * - Dependency installation
 * - Git and CI/CD setup
 * - Swagger API test generation
 *
 * <AUTHOR> Bootstrapper AI
 * @version 1.0.0
 */

// Core Node.js and utility dependencies
const chalk = require('chalk');        // Terminal string styling
const inquirer = require('inquirer');  // Interactive command line prompts
const execa = require('execa');        // Better child process execution
const fs = require('fs-extra');        // Enhanced file system operations
const ora = require('ora');            // Elegant terminal spinners

// Import custom modules for specific functionality
const { createFolders } = require('../lib/folders');           // Project folder structure creation
const { generateBoilerplateFiles } = require('../lib/generateFiles'); // File generation logic
const { installDependencies } = require('../lib/dependencies'); // NPM dependency management
const { promptUser } = require('../lib/prompts');              // User input collection
const { initGit, setupGitHubActions } = require('../lib/git');  // Git and CI/CD setup

/**
 * Validates the Node.js environment and displays version information
 * Ensures compatibility with Cypress and other dependencies
 *
 * @returns {boolean} Always returns true after validation
 */
function checkEnvironment() {
  const nodeVersion = process.version;
  const versionNum = parseInt(nodeVersion.slice(1).split('.')[0]);

  // Warn if Node.js version is too old for optimal Cypress performance
  if (versionNum < 14) {
    console.log(chalk.yellow('⚠️  Warning: Node.js version is below 14. Some features may not work correctly.'));
  }

  console.log(chalk.blue(`Using Node ${nodeVersion}`));
  return true;
}

/**
 * Initializes NPM in the current directory if package.json doesn't exist
 * Creates a basic package.json with default values
 *
 * @returns {Promise<void>}
 */
async function initializeNpm() {
  if (!await fs.pathExists('package.json')) {
    await execa('npm', ['init', '-y']);
  }
}

/**
 * Main initialization function that orchestrates the entire setup process
 * This function handles the complete workflow from user input to project completion
 *
 * Process flow:
 * 1. Environment validation
 * 2. User input collection
 * 3. Project structure creation
 * 4. File generation (including Swagger tests if requested)
 * 5. Dependency installation
 * 6. Git initialization (optional)
 * 7. CI/CD setup (optional)
 * 8. Final summary and next steps
 *
 * @returns {Promise<void>}
 */
async function init() {
  // Display welcome banner
  console.log(chalk.green.bold('\n🚀 Cypress Project Scaffold CLI\n'));

  // Validate Node.js environment
  checkEnvironment();

  try {
    // Step 1: Collect user preferences through interactive prompts
    const userChoices = await promptUser();

    // Initialize tracking arrays for setup summary
    const foldersCreated = [];
    const filesGenerated = [];

    // Define base dependencies that will be installed
    // Note: This list is maintained here for summary purposes only
    // Actual installation is handled by the dependencies module
    const dependenciesInstalled = [
      'cypress',                        // Core Cypress testing framework
      'cypress-plugin-api',             // API testing plugin
      'cypress-xpath',                  // XPath selector support
      'cypress-mochawesome-reporter',   // Enhanced test reporting
      'faker',                          // Test data generation
      'chai-json-schema',               // JSON schema validation
      ...(userChoices.useTypeScript ? [
        'typescript',                   // TypeScript compiler
        'ts-node',                      // TypeScript execution engine
        '@types/node',                  // Node.js type definitions
        '@types/fs-extra',              // fs-extra type definitions
        '@types/inquirer',              // inquirer type definitions
        '@types/cypress'                // Cypress type definitions
      ] : [])
    ];

    // Step 2: Create project folder structure
    let spinner = ora('Creating project folders...').start();
    const folders = createFolders();
    foldersCreated.push(...folders);
    spinner.succeed('Project folders created.');

    // Step 3: Generate all boilerplate files (config, tests, schemas, etc.)
    spinner = ora('Generating boilerplate files...').start();
    const generated = await generateBoilerplateFiles(userChoices);
    filesGenerated.push(...generated);
    spinner.succeed('Boilerplate files generated.');

    // Step 4: Ensure package.json exists for dependency management
    spinner = ora('Checking for package.json...').start();
    await initializeNpm();
    filesGenerated.push('package.json');
    spinner.succeed('package.json is ready.');

    // Step 5: Install all required dependencies
    spinner = ora('Installing dependencies...').start();
    await installDependencies(userChoices.useTypeScript);
    spinner.succeed('Dependencies installed.');

    // Step 6: Initialize Git repository if requested
    if (userChoices.gitInit) {
      spinner = ora('Initializing Git...').start();
      await initGit();
      filesGenerated.push('.git initialized');
      spinner.succeed('Git repository initialized.');
    }

    // Step 7: Set up GitHub Actions CI/CD workflow if requested
    if (userChoices.includeGitHubCI) {
      spinner = ora('Setting up GitHub Actions CI workflow...').start();
      await setupGitHubActions();
      filesGenerated.push('.github/workflows/cypress.yml');
      spinner.succeed('GitHub Actions CI workflow created.');
    }

    console.log(chalk.green.bold('\n✅ Cypress Automation Framework Setup Complete!\n'));

    // Step 8: Display completion summary and statistics
    console.log(chalk.yellow('📦 Setup Summary'));
    console.log(chalk.gray('-------------------------'));
    console.log(chalk.cyan(`📁 Folders created:        `), chalk.white(foldersCreated.length));
    console.log(chalk.cyan(`📄 Files generated:        `), chalk.white(filesGenerated.length));
    console.log(chalk.cyan(`📦 Dependencies installed: `), chalk.white(dependenciesInstalled.length));
    console.log(chalk.gray('-------------------------\n'));

    // Step 9: Offer next steps to the user
    const { runChoice } = await inquirer.prompt([
      {
        type: 'list',
        name: 'runChoice',
        message: 'What do you want to do now?',
        choices: [
          { name: '🟢 Open Cypress Test Runner (GUI)', value: 'open' },
          { name: '⚙️  Run tests in headless mode', value: 'run' },
          { name: '❌ Exit', value: 'exit' },
        ],
      },
    ]);

    // Execute user's choice for next steps
    if (runChoice === 'open') {
      // Launch Cypress GUI for interactive test development
      await execa('npx', ['cypress', 'open'], { stdio: 'inherit' });
    } else if (runChoice === 'run') {
      // Run tests in headless mode for CI/CD or quick validation
      spinner = ora('Running Cypress tests...').start();
      try {
        await execa('npx', ['cypress', 'run'], { stdio: 'inherit' });
        spinner.succeed('Cypress tests completed successfully.');
      } catch (error) {
        spinner.fail('Cypress tests failed.');
        console.error(chalk.red('Error running Cypress:'), error.message);
      }
    } else {
      // User chose to exit - provide helpful information
      console.log(chalk.gray('Exited without running tests.'));
      console.log(chalk.blue('\n💡 To run tests later:'));
      console.log(chalk.gray('  • GUI mode: npx cypress open'));
      console.log(chalk.gray('  • Headless: npx cypress run'));
    }
  } catch (error) {
    // Handle any errors during the setup process
    console.error(chalk.red('❌ Setup failed:'), error.message);
    console.log(chalk.yellow('\n🔧 Troubleshooting tips:'));
    console.log(chalk.gray('  • Ensure you have Node.js 14+ installed'));
    console.log(chalk.gray('  • Check your internet connection for dependency downloads'));
    console.log(chalk.gray('  • Verify you have write permissions in this directory'));
    process.exit(1);
  }
}

// Start the bootstrap process
init();
