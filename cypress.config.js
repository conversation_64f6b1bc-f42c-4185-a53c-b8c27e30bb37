/**
 * Cypress Configuration File
 * Generated by Cypress Bootstrapper AI
 */

const { defineConfig } = require('cypress');

module.exports = defineConfig({
  e2e: {
    // Base URL for the application under test
    baseUrl: 'https://automationexercise.com/',

    // Default browser for test execution
    browser: 'chrome',

    // Viewport settings
    viewportWidth: 1920,
    viewportHeight: 1080,

    // Timeout settings
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,

    // Node events configuration
    setupNodeEvents(on, config) {
      // Configure mochawesome reporter
      require('cypress-mochawesome-reporter/plugin')(on);

      // Browser launch configuration
      on('before:browser:launch', (browser = {}, launchOptions) => {
        if (browser.name === 'chrome' && browser.isHeadless) {
          launchOptions.args.push('--window-size=1920,1080');
        }
        return launchOptions;
      });

      return config;
    },

    // File patterns and locations
    specPattern: 'cypress/e2e/**/*.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/e2e.js',
    fixturesFolder: 'cypress/fixtures',
    screenshotsFolder: 'cypress/screenshots',
    videosFolder: 'cypress/videos'
  },

  // Reporter configuration
  reporter: 'cypress-mochawesome-reporter',
  reporterOptions: {
    reportDir: 'cypress/reports',
    overwrite: false,
    html: true,
    json: true
  }
});
