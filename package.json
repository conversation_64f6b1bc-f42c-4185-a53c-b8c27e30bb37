{"name": "cypress-bootstrapper", "version": "1.0.21", "description": "", "main": "cypress.config.js", "bin": {"cypress-bootstrapperai": "bin/bootstrap.js"}, "directories": {"lib": "lib"}, "dependencies": {"aggregate-error": "^3.1.0", "ansi-colors": "^4.1.3", "ansi-escapes": "^4.3.2", "ansi-regex": "^5.0.1", "ansi-styles": "^4.3.0", "arch": "^2.2.0", "argparse": "^2.0.1", "asn1": "^0.2.6", "assert-plus": "^1.0.0", "astral-regex": "^2.0.0", "async": "^3.2.6", "asynckit": "^0.4.0", "at-least-node": "^1.0.0", "aws-sign2": "^0.7.0", "aws4": "^1.13.2", "axios": "^0.27.2", "balanced-match": "^1.0.2", "base64-js": "^1.5.1", "bcrypt-pbkdf": "^1.0.2", "bl": "^4.1.0", "blob-util": "^2.0.2", "bluebird": "^3.7.2", "brace-expansion": "^2.0.2", "browser-stdout": "^1.3.1", "buffer": "^5.7.1", "buffer-crc32": "^0.2.13", "cachedir": "^2.4.0", "call-bind-apply-helpers": "^1.0.2", "call-bound": "^1.0.4", "camelcase": "^6.3.0", "caseless": "^0.12.0", "chalk": "^4.1.2", "chardet": "^0.7.0", "check-more-types": "^2.24.0", "chokidar": "^4.0.3", "ci-info": "^4.2.0", "clean-stack": "^2.2.0", "cli-cursor": "^3.1.0", "cli-spinners": "^2.9.2", "cli-table3": "^0.6.1", "cli-truncate": "^2.1.0", "cli-width": "^3.0.0", "cliui": "^8.0.1", "clone": "^1.0.4", "color-convert": "^2.0.1", "color-name": "^1.1.4", "colorette": "^2.0.20", "colors": "^1.4.0", "combined-stream": "^1.0.8", "commander": "^6.2.1", "common-tags": "^1.8.2", "concat-map": "^0.0.1", "core-util-is": "^1.0.2", "cross-spawn": "^7.0.6", "csstype": "^3.1.3", "dashdash": "^1.14.1", "dateformat": "^4.6.3", "dayjs": "^1.11.13", "debug": "^4.4.1", "decamelize": "^4.0.0", "defaults": "^1.0.4", "delayed-stream": "^1.0.0", "diff": "^7.0.0", "dunder-proto": "^1.0.1", "eastasianwidth": "^0.2.0", "ecc-jsbn": "^0.1.2", "emoji-regex": "^8.0.0", "end-of-stream": "^1.4.4", "enquirer": "^2.4.1", "entities": "^4.5.0", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "escalade": "^3.2.0", "escape-html": "^1.0.3", "escape-string-regexp": "^1.0.5", "estree-walker": "^2.0.2", "eventemitter2": "^6.4.7", "execa": "^5.1.1", "executable": "^4.1.1", "extend": "^3.0.2", "external-editor": "^3.1.0", "extract-zip": "^2.0.1", "extsprintf": "^1.3.0", "fd-slicer": "^1.1.0", "figures": "^3.2.0", "find-up": "^5.0.0", "flat": "^5.0.2", "follow-redirects": "^1.15.9", "foreground-child": "^3.3.1", "forever-agent": "^0.6.1", "form-data": "^4.0.3", "fs-extra": "^10.1.0", "fs.realpath": "^1.0.0", "fsu": "^1.1.1", "function-bind": "^1.1.2", "get-caller-file": "^2.0.5", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "get-stream": "^6.0.1", "getos": "^3.2.1", "getpass": "^0.1.7", "glob": "^10.4.5", "global-dirs": "^3.0.1", "gopd": "^1.2.0", "graceful-fs": "^4.2.11", "has-flag": "^4.0.0", "has-symbols": "^1.1.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2", "he": "^1.2.0", "highlight.js": "^11.4.0", "http-signature": "^1.4.0", "human-signals": "^2.1.0", "iconv-lite": "^0.4.24", "ieee754": "^1.2.1", "indent-string": "^4.0.0", "inflight": "^1.0.6", "inherits": "^2.0.4", "ini": "^2.0.0", "inquirer": "^8.2.6", "is-fullwidth-code-point": "^3.0.0", "is-installed-globally": "^0.4.0", "is-interactive": "^1.0.0", "is-path-inside": "^3.0.3", "is-plain-obj": "^2.1.0", "is-stream": "^2.0.1", "is-typedarray": "^1.0.0", "is-unicode-supported": "^0.1.0", "isexe": "^2.0.0", "isstream": "^0.1.2", "jackspeak": "^3.4.3", "js-tokens": "^4.0.0", "js-yaml": "^4.1.0", "jsbn": "^0.1.1", "json-schema": "^0.4.0", "json-stringify-safe": "^5.0.1", "jsonfile": "^6.1.0", "jsprim": "^2.0.2", "lazy-ass": "^1.6.0", "listr2": "^3.14.0", "locate-path": "^6.0.0", "lodash": "^4.17.21", "lodash.isempty": "^4.4.0", "lodash.isfunction": "^3.0.9", "lodash.isobject": "^3.0.2", "lodash.isstring": "^4.0.1", "lodash.once": "^4.1.1", "log-symbols": "^4.1.0", "log-update": "^4.0.0", "loose-envify": "^1.4.0", "lru-cache": "^10.4.3", "magic-string": "^0.30.17", "math-intrinsics": "^1.1.0", "merge-stream": "^2.0.0", "mime-db": "^1.52.0", "mime-types": "^2.1.35", "mimic-fn": "^2.1.0", "minimatch": "^9.0.5", "minimist": "^1.2.8", "minipass": "^7.1.2", "mochawesome": "^7.1.3", "mochawesome-merge": "^4.4.1", "mochawesome-report-generator": "^6.2.0", "ms": "^2.1.3", "mute-stream": "^0.0.8", "nanoid": "^3.3.11", "npm-run-path": "^4.0.1", "object-assign": "^4.1.1", "object-inspect": "^1.13.4", "once": "^1.4.0", "onetime": "^5.1.2", "opener": "^1.5.2", "ora": "^5.4.1", "os-tmpdir": "^1.0.2", "ospath": "^1.2.2", "p-limit": "^3.1.0", "p-locate": "^5.0.0", "p-map": "^4.0.0", "p-try": "^2.2.0", "package-json-from-dist": "^1.0.1", "path-exists": "^4.0.0", "path-is-absolute": "^1.0.1", "path-key": "^3.1.1", "path-scurry": "^1.11.1", "pend": "^1.2.0", "performance-now": "^2.1.0", "picocolors": "^1.1.1", "pify": "^2.3.0", "postcss": "^8.5.5", "pretty-bytes": "^5.6.0", "prismjs": "^1.30.0", "process": "^0.11.10", "prop-types": "^15.8.1", "proxy-from-env": "^1.0.0", "pump": "^3.0.2", "qs": "^6.14.0", "randombytes": "^2.1.0", "react-is": "^16.13.1", "readable-stream": "^3.6.2", "readdirp": "^4.1.2", "request-progress": "^3.0.0", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "restore-cursor": "^3.1.0", "rfdc": "^1.4.1", "run-async": "^2.4.1", "rxjs": "^7.8.2", "safe-buffer": "^5.2.1", "safer-buffer": "^2.1.2", "semver": "^7.7.2", "serialize-javascript": "^6.0.2", "set-blocking": "^2.0.0", "set-cookie-parser": "^2.7.1", "shebang-command": "^2.0.0", "shebang-regex": "^3.0.0", "side-channel": "^1.1.0", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2", "signal-exit": "^3.0.7", "slice-ansi": "^3.0.0", "source-map-js": "^1.2.1", "sshpk": "^1.18.0", "string_decoder": "^1.3.0", "string-width": "^4.2.3", "string-width-cjs": "^4.2.3", "strip-ansi": "^6.0.1", "strip-ansi-cjs": "^6.0.1", "strip-final-newline": "^2.0.0", "strip-json-comments": "^3.1.1", "supports-color": "^7.2.0", "tcomb": "^3.2.29", "tcomb-validation": "^3.4.1", "throttleit": "^1.0.1", "through": "^2.3.8", "tldts": "^6.1.86", "tldts-core": "^6.1.86", "tmp": "^0.0.33", "tough-cookie": "^5.1.2", "tree-kill": "^1.2.2", "tslib": "^2.8.1", "tunnel-agent": "^0.6.0", "tweetnacl": "^0.14.5", "type-fest": "^0.21.3", "undici-types": "^7.8.0", "universalify": "^2.0.1", "untildify": "^4.0.0", "util-deprecate": "^1.0.2", "uuid": "^8.3.2", "validator": "^13.15.15", "verror": "^1.10.0", "vue": "^3.5.16", "wcwidth": "^1.0.1", "which": "^2.0.2", "which-module": "^2.0.1", "workerpool": "^9.3.2", "wrap-ansi": "^6.2.0", "wrap-ansi-cjs": "^7.0.0", "wrappy": "^1.0.2", "y18n": "^5.0.8", "yargs": "^17.7.2", "yargs-parser": "^21.1.1", "yargs-unparser": "^2.0.0", "yauzl": "^2.10.0", "yocto-queue": "^0.1.0"}, "devDependencies": {"@types/cypress": "^1.1.6", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.8", "@types/node": "^24.0.1", "chai-json-schema": "^1.5.1", "cypress": "^14.5.0", "cypress-mochawesome-reporter": "^3.8.2", "cypress-plugin-api": "^2.11.2", "cypress-xpath": "^2.0.1", "faker": "^6.6.6", "mocha": "^11.6.0", "openai": "^5.8.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "scripts": {"test": "mocha"}, "keywords": [], "author": "", "license": "ISC"}