{"name": "cypress-mochawesome-reporter", "version": "3.8.2", "description": "Zero config <PERSON><PERSON><PERSON><PERSON> reporter for Cypress with screenshots", "engines": {"node": ">=14"}, "author": "<PERSON><PERSON>", "license": "MIT", "funding": "https://github.com/sponsors/LironEr", "repository": {"type": "git", "url": "https://github.com/LironEr/cypress-mochawesome-reporter"}, "main": "lib/reporter.js", "types": "index.d.ts", "bin": {"generate-mochawesome-report": "cli.js"}, "scripts": {"lerna": "lerna", "install-examples": "lerna exec --concurrency 1 --scope @example/* \"npm i\"", "test:prepare": "lerna run --stream --no-bail --concurrency 1 --scope @example/* test", "test": "cypress run", "cypress:open": "cypress open"}, "keywords": ["cypress", "mochawesome", "mocha", "reporter", "screenshot", "video"], "dependencies": {"commander": "^10.0.1", "fs-extra": "^10.0.1", "mochawesome": "^7.1.3", "mochawesome-merge": "^4.2.1", "mochawesome-report-generator": "^6.2.0"}, "devDependencies": {"@badeball/cypress-cucumber-preprocessor": "^19.2.0", "cypress": "^13.1.0", "lerna": "^7.1.4"}, "peerDependencies": {"cypress": ">=6.2.0"}, "files": ["lib", "register.js", "plugin.js", "index.d.ts", "cucumberSupport.js"]}