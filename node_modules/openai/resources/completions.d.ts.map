{"version": 3, "file": "completions.d.ts", "sourceRoot": "", "sources": ["../src/resources/completions.ts"], "names": [], "mappings": "OAEO,EAAE,WAAW,EAAE;OACf,KAAK,cAAc;OACnB,KAAK,yBAAyB;OAC9B,EAAE,UAAU,EAAE;OACd,EAAE,MAAM,EAAE;OACV,EAAE,cAAc,EAAE;AAEzB,qBAAa,WAAY,SAAQ,WAAW;IAC1C;;;;;;;;;;OAUG;IACH,MAAM,CAAC,IAAI,EAAE,kCAAkC,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,UAAU,CAAC,UAAU,CAAC;IAClG,MAAM,CAAC,IAAI,EAAE,+BAA+B,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IACvG,MAAM,CACJ,IAAI,EAAE,0BAA0B,EAChC,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;CAS/C;AAED;;;GAGG;AACH,MAAM,WAAW,UAAU;IACzB;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,OAAO,EAAE,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAEjC;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,MAAM,EAAE,iBAAiB,CAAC;IAE1B;;;;;OAKG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAE5B;;OAEG;IACH,KAAK,CAAC,EAAE,eAAe,CAAC;CACzB;AAED,MAAM,WAAW,gBAAgB;IAC/B;;;;;OAKG;IACH,aAAa,EAAE,MAAM,GAAG,QAAQ,GAAG,gBAAgB,CAAC;IAEpD,KAAK,EAAE,MAAM,CAAC;IAEd,QAAQ,EAAE,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC;IAE3C,IAAI,EAAE,MAAM,CAAC;CACd;AAED,yBAAiB,gBAAgB,CAAC;IAChC,UAAiB,QAAQ;QACvB,WAAW,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAE5B,cAAc,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAE/B,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAEvB,YAAY,CAAC,EAAE,KAAK,CAAC;YAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;SAAE,CAAC,CAAC;KACjD;CACF;AAED;;GAEG;AACH,MAAM,WAAW,eAAe;IAC9B;;OAEG;IACH,iBAAiB,EAAE,MAAM,CAAC;IAE1B;;OAEG;IACH,aAAa,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,yBAAyB,CAAC,EAAE,eAAe,CAAC,uBAAuB,CAAC;IAEpE;;OAEG;IACH,qBAAqB,CAAC,EAAE,eAAe,CAAC,mBAAmB,CAAC;CAC7D;AAED,yBAAiB,eAAe,CAAC;IAC/B;;OAEG;IACH,UAAiB,uBAAuB;QACtC;;;WAGG;QACH,0BAA0B,CAAC,EAAE,MAAM,CAAC;QAEpC;;WAEG;QACH,YAAY,CAAC,EAAE,MAAM,CAAC;QAEtB;;WAEG;QACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;QAE1B;;;;;WAKG;QACH,0BAA0B,CAAC,EAAE,MAAM,CAAC;KACrC;IAED;;OAEG;IACH,UAAiB,mBAAmB;QAClC;;WAEG;QACH,YAAY,CAAC,EAAE,MAAM,CAAC;QAEtB;;WAEG;QACH,aAAa,CAAC,EAAE,MAAM,CAAC;KACxB;CACF;AAED,MAAM,MAAM,sBAAsB,GAAG,kCAAkC,GAAG,+BAA+B,CAAC;AAE1G,MAAM,WAAW,0BAA0B;IACzC;;;;;;OAMG;IACH,KAAK,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,wBAAwB,GAAG,aAAa,GAAG,aAAa,CAAC;IAEhF;;;;;;;OAOG;IACH,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;IAE7E;;;;;;;;;;OAUG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAExB;;OAEG;IACH,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAEtB;;;;;;OAMG;IACH,iBAAiB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAElC;;;;;;;;;;;;;OAaG;IACH,UAAU,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,GAAG,IAAI,CAAC;IAE9C;;;;;;;OAOG;IACH,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEzB;;;;;;;;OAQG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE3B;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAElB;;;;;;OAMG;IACH,gBAAgB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEjC;;;;;;;OAOG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAErB;;;;;OAKG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IAErC;;;;;;;OAOG;IACH,MAAM,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAExB;;OAEG;IACH,cAAc,CAAC,EAAE,yBAAyB,CAAC,2BAA2B,GAAG,IAAI,CAAC;IAE9E;;;;OAIG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEvB;;;;;;OAMG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE5B;;;;;;OAMG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEtB;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,yBAAiB,sBAAsB,CAAC;IACtC,KAAY,kCAAkC,GAAG,cAAc,CAAC,kCAAkC,CAAC;IACnG,KAAY,+BAA+B,GAAG,cAAc,CAAC,+BAA+B,CAAC;CAC9F;AAED,MAAM,WAAW,kCAAmC,SAAQ,0BAA0B;IACpF;;;;;;;OAOG;IACH,MAAM,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC;CACvB;AAED,MAAM,WAAW,+BAAgC,SAAQ,0BAA0B;IACjF;;;;;;;OAOG;IACH,MAAM,EAAE,IAAI,CAAC;CACd;AAED,MAAM,CAAC,OAAO,WAAW,WAAW,CAAC;IACnC,OAAO,EACL,KAAK,UAAU,IAAI,UAAU,EAC7B,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,eAAe,IAAI,eAAe,EACvC,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,kCAAkC,IAAI,kCAAkC,EAC7E,KAAK,+BAA+B,IAAI,+BAA+B,GACxE,CAAC;CACH"}