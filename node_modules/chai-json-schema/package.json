{"name": "chai-json-schema", "version": "1.5.1", "description": "Chai plugin for JSON Schema v4", "author": {"name": "<PERSON>", "url": "https://github.com/chaijs"}, "keywords": ["array", "assert", "assertion", "browser", "chai", "chai-plugin", "json", "json-schema", "objects", "schema", "test", "testing"], "homepage": "http://chaijs.com", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/chaijs/chai-json-schema.git"}, "bugs": {"url": "https://github.com/chaijs/chai-json-schema/issues"}, "main": "./index", "engines": {"node": ">= 6"}, "scripts": {"test": "grunt test"}, "dependencies": {"jsonpointer.js": "0.4.0", "tv4": "^1.3.0"}, "devDependencies": {"chai": "^4.2.0", "grunt": "^1.0.1", "grunt-bump": "^0.8.0", "grunt-cli": "^1.2.0", "grunt-continue": "^0.1.0", "grunt-contrib-jshint": "^2.1.0", "grunt-mocha": "^1.0.2", "grunt-mocha-test": "^0.13.2", "jshint-path-reporter": "^0.1.3", "mocha": "^6.1.4", "mocha-unfunk-reporter": "^0.4.0", "requirejs": "^2.2.0"}, "peerDependencies": {"chai": ">= 1.6.1 < 5"}}