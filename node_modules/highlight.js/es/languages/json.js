/*
Language: JSON
Description: J<PERSON>N (JavaScript Object Notation) is a lightweight data-interchange format.
Author: <PERSON> <<EMAIL>>
Website: http://www.json.org
Category: common, protocols, web
*/

function json(hljs) {
  const ATTRIBUTE = {
    className: 'attr',
    begin: /"(\\.|[^\\"\r\n])*"(?=\s*:)/,
    relevance: 1.01
  };
  const PUNCTUATION = {
    match: /[{}[\],:]/,
    className: "punctuation",
    relevance: 0
  };
  // normally we would rely on `keywords` for this but using a mode here allows us
  // to use the very tight `illegal: \S` rule later to flag any other character
  // as illegal indicating that despite looking like JSON we do not truly have
  // JSO<PERSON> and thus improve false-positively greatly since JSON will try and claim
  // all sorts of JSON looking stuff
  const LITERALS = {
    beginKeywords: [
      "true",
      "false",
      "null"
    ].join(" ")
  };

  return {
    name: '<PERSON><PERSON><PERSON>',
    contains: [
      ATTRIBUTE,
      PUNCTUATION,
      hljs.QUOTE_STRING_MODE,
      LITERALS,
      hljs.C_NUMBER_MODE,
      hljs.C_LINE_COMMENT_MODE,
      hljs.C_BLOCK_COMMENT_MODE
    ],
    illegal: '\\S'
  };
}

export { json as default };
