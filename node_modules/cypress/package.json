{"name": "cypress", "version": "14.4.1", "main": "index.js", "scripts": {"postinstall": "node index.js --exec install", "size": "t=\"$(npm pack .)\"; wc -c \"${t}\"; tar tvf \"${t}\"; rm \"${t}\";"}, "dependencies": {"@cypress/request": "^3.0.8", "@cypress/xvfb": "^1.2.4", "@types/sinonjs__fake-timers": "8.1.1", "@types/sizzle": "^2.3.2", "arch": "^2.2.0", "blob-util": "^2.0.2", "bluebird": "^3.7.2", "buffer": "^5.7.1", "cachedir": "^2.3.0", "chalk": "^4.1.0", "check-more-types": "^2.24.0", "ci-info": "^4.1.0", "cli-cursor": "^3.1.0", "cli-table3": "0.6.1", "commander": "^6.2.1", "common-tags": "^1.8.0", "dayjs": "^1.10.4", "debug": "^4.3.4", "enquirer": "^2.3.6", "eventemitter2": "6.4.7", "execa": "4.1.0", "executable": "^4.1.1", "extract-zip": "2.0.1", "figures": "^3.2.0", "fs-extra": "^9.1.0", "getos": "^3.2.1", "is-installed-globally": "~0.4.0", "lazy-ass": "^1.6.0", "listr2": "^3.8.3", "lodash": "^4.17.21", "log-symbols": "^4.0.0", "minimist": "^1.2.8", "ospath": "^1.2.2", "pretty-bytes": "^5.6.0", "process": "^0.11.10", "proxy-from-env": "1.0.0", "request-progress": "^3.0.0", "semver": "^7.7.1", "supports-color": "^8.1.1", "tmp": "~0.2.3", "tree-kill": "1.2.2", "untildify": "^4.0.0", "yauzl": "^2.10.0"}, "files": ["bin", "lib", "index.js", "index.mjs", "types/**/*.d.ts", "mount-utils", "vue", "react", "angular", "svelte"], "bin": {"cypress": "bin/cypress"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "types": "types", "exports": {".": {"types": "./types/index.d.ts", "import": "./index.mjs", "require": "./index.js"}, "./vue": {"types": "./vue/dist/index.d.ts", "import": "./vue/dist/cypress-vue.esm-bundler.js", "require": "./vue/dist/cypress-vue.cjs.js"}, "./package.json": {"import": "./package.json", "require": "./package.json"}, "./react": {"types": "./react/dist/index.d.ts", "import": "./react/dist/cypress-react.esm-bundler.js", "require": "./react/dist/cypress-react.cjs.js"}, "./mount-utils": {"types": "./mount-utils/dist/index.d.ts", "require": "./mount-utils/dist/index.js"}, "./angular": {"types": "./angular/dist/index.d.ts", "import": "./angular/dist/index.js", "require": "./angular/dist/index.js"}, "./svelte": {"types": "./svelte/dist/index.d.ts", "import": "./svelte/dist/cypress-svelte.esm-bundler.js", "require": "./svelte/dist/cypress-svelte.cjs.js"}}, "nx": {"targets": {"build-cli": {"dependsOn": ["prebuild"], "outputs": ["{projectRoot}/types", "{projectRoot}/build"]}}, "implicitDependencies": ["@cypress/*"]}, "buildInfo": {"commitBranch": "develop", "commitSha": "a0a4eb1b2c1baac9f4d6ad4b02a3db44b4abd70b", "commitDate": "2025-06-03T17:17:49.000Z", "stable": true}, "description": "Cypress is a next generation front end testing tool built for the modern web", "homepage": "https://cypress.io", "license": "MIT", "bugs": {"url": "https://github.com/cypress-io/cypress/issues"}, "repository": {"type": "git", "url": "https://github.com/cypress-io/cypress.git"}, "keywords": ["automation", "browser", "cypress", "cypress.io", "e2e", "end-to-end", "integration", "component", "mocks", "runner", "spies", "stubs", "test", "testing"]}