{"author": "<PERSON><PERSON> (https://filiphric.com/)", "license": "ISC", "name": "cypress-plugin-api", "version": "2.11.2", "keywords": ["cypress", "api", "testing", "plugin"], "description": "UI for testing API in Cypress", "main": "dist/support.js", "types": "dist/types.d.ts", "files": ["/dist/*"], "scripts": {"test": "cypress run", "cy:open": "cypress open", "cy:run": "cypress run", "start": "node ./server", "build": "vite build", "build:watch": "vite build --watch", "lint": "tsc --noEmit && eslint . --max-warnings=15 --quiet", "semantic-release": "semantic-release", "release": "standard-version", "release:minor": "standard-version --release-as minor", "release:patch": "standard-version --release-as patch", "release:major": "standard-version --release-as major"}, "pre-commit": ["lint"], "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/github", "@semantic-release/npm"]}, "devDependencies": {"@commitlint/cli": "^17.2.0", "@commitlint/config-conventional": "^17.2.0", "@rollup/plugin-typescript": "^11.0.0", "@semantic-release/changelog": "^6.0.1", "@types/node": "^18.11.8", "@types/prismjs": "^1.26.0", "@types/set-cookie-parser": "^2.4.2", "@typescript-eslint/eslint-plugin": "^5.42.1", "@typescript-eslint/parser": "^5.42.1", "@vitejs/plugin-vue": "^3.1.0", "autoprefixer": "^10.4.12", "cypress": "^12.5.1", "eslint": "^8.27.0", "eslint-plugin-no-only-tests": "^3.1.0", "eslint-plugin-vue": "^9.7.0", "express": "4.17.1", "generate-changelog": "^1.8.0", "husky": "^8.0.2", "postcss": "^8.4.17", "pre-commit": "^1.2.2", "semantic-release": "^19.0.5", "standard-version": "^9.5.0", "tailwindcss": "^3.1.8", "typescript": "4.5.4", "vite": "^3.1.4"}, "peerDependencies": {"cypress": ">=10"}, "dependencies": {"highlight.js": "11.4.0", "prismjs": "^1.29.0", "set-cookie-parser": "^2.5.1", "vue": "^3.2.41"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/filiphric/cypress-plugin-api.git"}, "bugs": {"url": "https://github.com/filiphric/cypress-plugin-api/issues"}, "homepage": "https://github.com/filiphric/cypress-plugin-api#readme"}