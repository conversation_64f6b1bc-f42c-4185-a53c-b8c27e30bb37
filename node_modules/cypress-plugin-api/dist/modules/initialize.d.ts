/// <reference types="cypress" />
export declare const initialize: () => {
    app: import("vue").App<Element>;
    props: {
        id: string;
        method: string;
        status: string;
        time: number;
        size: string;
        url: string;
        query: {
            body: Record<string, any>;
            formatted: string;
        };
        auth: {
            body: Record<string, any>;
            formatted: string;
        };
        requestHeaders: {
            body: Record<string, any>;
            formatted: string;
        };
        requestBody: {
            body: Cypress.RequestBody;
            formatted: string;
        };
        responseBody: {
            body: Record<string, any>;
            formatted: string;
        };
        responseHeaders: {
            body: Record<string, any>;
            formatted: string;
        };
        cookies: {
            body: Record<string, any>;
        };
    }[];
};
