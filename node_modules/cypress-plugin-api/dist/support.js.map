{"version": 3, "file": "support.js", "sources": ["../src/utils/validateMethod.ts", "../src/utils/resolveOptions.ts", "../src/utils/anonymize.ts", "../src/utils/getState.ts", "../src/modules/removeStyles.ts", "../node_modules/set-cookie-parser/lib/set-cookie.js", "../src/utils/convertSize.ts", "../src/utils/isValidJson.ts", "../src/utils/calculateSize.ts", "../node_modules/prismjs/prism.js", "../node_modules/prismjs/components/prism-json.js", "../src/modules/transform.ts", "../src/utils/isValidXml.ts", "../src/utils/isValidHtml.ts", "../src/utils/isValidBlob.ts", "../src/utils/getFormat.ts", "../src/modules/handleResponse.ts", "../src/components/TitlePanel.vue", "../src/components/StatusPanel.vue", "../src/modules/addStyles.ts", "../src/modules/mountPlugin.ts", "../src/modules/initialize.ts", "../src/modules/transformData.ts", "../src/utils/isValidUrl.ts", "../src/utils/isValidIp.ts", "../src/utils/isValidUrlOrIp.ts", "../src/modules/cloneProps.ts", "../src/modules/api.ts", "../src/support.ts"], "sourcesContent": ["const { _ } = Cypress\n\nexport const methods = [\n  'GET',\n  'POST',\n  'PUT',\n  'HEAD',\n  'DELETE',\n  'OPTIONS',\n  'TRACE',\n  'COPY',\n  'LOCK',\n  'MKCOL',\n  'MOVE',\n  'PURGE',\n  'PROPFIND',\n  'PROPPATCH',\n  '<PERSON>LOCK',\n  'REPORT',\n  'MKACTIVITY',\n  'CHECKOUT',\n  'MERGE',\n  'M-SEARCH',\n  'NOTIFY',\n  'SUBSCRIBE',\n  'UNSUBSCRIBE',\n  'PATCH',\n  'SEARCH',\n  'CONNECT'\n]\n\nexport const validateMethod = (str: string) => {\n  return _.isString(str) && _.includes(methods, str.toUpperCase())\n}", "import { validate<PERSON>ethod } from './validateMethod';\nconst { _ } = Cypress\n\nexport const resolveOptions = (...args: any[]) => {\n\n  if (_.isFunction(args[0])) {\n    args.shift()\n  }\n\n  const o: any = {}\n  const userOptions = o\n\n  if (_.isObject(args[0])) {\n    _.extend(userOptions, args[0])\n  } else if (args.length === 1) {\n    o.url = args[0]\n  } else if (args.length === 2) {\n    if (validateMethod(args[0])) {\n      o.method = args[0]\n      o.url = args[1]\n    } else {\n      o.url = args[0]\n      o.body = args[1]\n    }\n  } else if (args.length === 3) {\n    o.method = args[0]\n    o.url = args[1]\n    o.body = args[2]\n  }\n\n  return userOptions\n\n}", "import { RequestProps, HideCredentialsOptions } from '../types';\n\nexport const anonymize = (options: RequestProps) => {\n\n  const optionsUndefined = Cypress.env('hideCredentialsOptions') === undefined\n\n  const anonymizeOptions: HideCredentialsOptions = {\n    auth: [],\n    body: [],\n    headers: [],\n    qs: [],\n    ...Cypress.env('hideCredentialsOptions')\n  }\n\n  if (optionsUndefined) {\n    // as defined here https://github.com/request/request#http-authentication\n    anonymizeOptions.auth?.push('user', 'username', 'pass', 'password', 'bearer')\n    anonymizeOptions.headers?.push('authorization', 'Authorization', 'password', 'username')\n    anonymizeOptions.body?.push('pass', 'password')\n  }\n\n  anonymizeOptions.auth?.forEach(k => {\n    if (options.auth.body && options.auth.body[k]) {\n      options.auth.body[k] = options?.auth.body[k].replace(/./g, '*')\n    }\n  })\n\n  anonymizeOptions.headers?.forEach(k => {\n    if (options.requestHeaders.body && options.requestHeaders.body[k]) {\n      options.requestHeaders.body[k] = options?.requestHeaders.body[k].replace(/./g, '*')\n    }\n  })\n\n  anonymizeOptions.body?.forEach(k => {\n    if (options.requestBody.body && options.requestBody.body[k as keyof Cypress.RequestBody]) {\n      // @ts-ignore until I figure out how to fix this\n      options.requestBody.body[k] = options?.requestBody.body[k].replace(/./g, '*')\n    }\n  })\n\n  anonymizeOptions.qs?.forEach(k => {\n    if (options.query.body && options.query.body[k as keyof Cypress.RequestBody]) {\n      // @ts-ignore until I figure out how to fix this\n      options.query.body[k] = options?.query.body[k].replace(/./g, '*')\n    }\n  })\n\n  return options\n}", "export const getState = () => {\n  // @ts-ignore cy.state() has no type definitions\n  const doc: Document = cy.state('document');\n  // @ts-ignore cy.state() has no type definitions\n  const attempt: number = cy.state('runnable')._currentRetry\n  // @ts-ignore cy.state() has no type definitions\n  const testId: string = cy.state('test').id\n  return { doc, attempt, testId }\n}", "import { getState } from \"../utils/getState\";\n\nexport const removeStyles = () => {\n\n  const { doc } = getState()\n\n  const style = doc.getElementById('api-plugin-styles');\n  style?.remove()\n\n}", "\"use strict\";\n\nvar defaultParseOptions = {\n  decodeValues: true,\n  map: false,\n  silent: false,\n};\n\nfunction isNonEmptyString(str) {\n  return typeof str === \"string\" && !!str.trim();\n}\n\nfunction parseString(setCookieValue, options) {\n  var parts = setCookieValue.split(\";\").filter(isNonEmptyString);\n\n  var nameValuePairStr = parts.shift();\n  var parsed = parseNameValuePair(nameValuePairStr);\n  var name = parsed.name;\n  var value = parsed.value;\n\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  try {\n    value = options.decodeValues ? decodeURIComponent(value) : value; // decode cookie value\n  } catch (e) {\n    console.error(\n      \"set-cookie-parser encountered an error while decoding a cookie with value '\" +\n        value +\n        \"'. Set options.decodeValues to false to disable this feature.\",\n      e\n    );\n  }\n\n  var cookie = {\n    name: name,\n    value: value,\n  };\n\n  parts.forEach(function (part) {\n    var sides = part.split(\"=\");\n    var key = sides.shift().trimLeft().toLowerCase();\n    var value = sides.join(\"=\");\n    if (key === \"expires\") {\n      cookie.expires = new Date(value);\n    } else if (key === \"max-age\") {\n      cookie.maxAge = parseInt(value, 10);\n    } else if (key === \"secure\") {\n      cookie.secure = true;\n    } else if (key === \"httponly\") {\n      cookie.httpOnly = true;\n    } else if (key === \"samesite\") {\n      cookie.sameSite = value;\n    } else {\n      cookie[key] = value;\n    }\n  });\n\n  return cookie;\n}\n\nfunction parseNameValuePair(nameValuePairStr) {\n  // Parses name-value-pair according to rfc6265bis draft\n\n  var name = \"\";\n  var value = \"\";\n  var nameValueArr = nameValuePairStr.split(\"=\");\n  if (nameValueArr.length > 1) {\n    name = nameValueArr.shift();\n    value = nameValueArr.join(\"=\"); // everything after the first =, joined by a \"=\" if there was more than one part\n  } else {\n    value = nameValuePairStr;\n  }\n\n  return { name: name, value: value };\n}\n\nfunction parse(input, options) {\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  if (!input) {\n    if (!options.map) {\n      return [];\n    } else {\n      return {};\n    }\n  }\n\n  if (input.headers && input.headers[\"set-cookie\"]) {\n    // fast-path for node.js (which automatically normalizes header names to lower-case\n    input = input.headers[\"set-cookie\"];\n  } else if (input.headers) {\n    // slow-path for other environments - see #25\n    var sch =\n      input.headers[\n        Object.keys(input.headers).find(function (key) {\n          return key.toLowerCase() === \"set-cookie\";\n        })\n      ];\n    // warn if called on a request-like object with a cookie header rather than a set-cookie header - see #34, 36\n    if (!sch && input.headers.cookie && !options.silent) {\n      console.warn(\n        \"Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning.\"\n      );\n    }\n    input = sch;\n  }\n  if (!Array.isArray(input)) {\n    input = [input];\n  }\n\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  if (!options.map) {\n    return input.filter(isNonEmptyString).map(function (str) {\n      return parseString(str, options);\n    });\n  } else {\n    var cookies = {};\n    return input.filter(isNonEmptyString).reduce(function (cookies, str) {\n      var cookie = parseString(str, options);\n      cookies[cookie.name] = cookie;\n      return cookies;\n    }, cookies);\n  }\n}\n\n/*\n  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas\n  that are within a single set-cookie field-value, such as in the Expires portion.\n\n  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2\n  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128\n  React Native's fetch does this for *every* header, including set-cookie.\n\n  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25\n  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation\n*/\nfunction splitCookiesString(cookiesString) {\n  if (Array.isArray(cookiesString)) {\n    return cookiesString;\n  }\n  if (typeof cookiesString !== \"string\") {\n    return [];\n  }\n\n  var cookiesStrings = [];\n  var pos = 0;\n  var start;\n  var ch;\n  var lastComma;\n  var nextStart;\n  var cookiesSeparatorFound;\n\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  }\n\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos);\n\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  }\n\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        // ',' is a cookie separator if we have later first '=', not ';' or ','\n        lastComma = pos;\n        pos += 1;\n\n        skipWhitespace();\n        nextStart = pos;\n\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n\n        // currently special character\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          // we found cookies separator\n          cookiesSeparatorFound = true;\n          // pos is inside the next cookie, so back up and return it.\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.substring(start, lastComma));\n          start = pos;\n        } else {\n          // in param ',' or param separator ';',\n          // we continue from that comma\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n    }\n  }\n\n  return cookiesStrings;\n}\n\nmodule.exports = parse;\nmodule.exports.parse = parse;\nmodule.exports.parseString = parseString;\nmodule.exports.splitCookiesString = splitCookiesString;\n", "export function convertSize(size: number) {\n  const i: number = size == 0 ? 0 : Math.floor(Math.log(size) / Math.log(1024));\n  return <never>(size / Math.pow(1024, i)).toFixed(2) * 1 + '\\u00A0' + ['B', 'kB', 'MB', 'GB', 'TB'][i];\n}", "export const isValidJson = (input: any): boolean => {\n  // all objects are JSONs\n  if (typeof input === 'object') {\n    return true;\n  } else if (typeof input === 'string') {\n    // strings are tested for JSON format\n    try {\n      JSON.parse(input);\n      return true;\n    } catch (e) {\n      return false;\n    }\n  } else {\n    return false;\n  }\n}", "import { isValid<PERSON><PERSON> } from \"./isValidJson\"\n\nexport const calculateSize = (value: object) => {\n\n  if (!value) {\n    return 0\n  }\n\n  const stringified = value.toString()\n  // remove all carriage return symbols\n  const cleanString = stringified.replace(/\\r\\n/g, '\\n')\n  // replace white spaces if value is a JSON\n  const finalString = isValidJson(cleanString) ? cleanString.replace(/\\s/g, '') : cleanString\n  const byteCount = new Blob([finalString]).size\n\n  return byteCount\n\n}\n", "\n/* **********************************************\n     Begin prism-core.js\n********************************************** */\n\n/// <reference lib=\"WebWorker\"/>\n\nvar _self = (typeof window !== 'undefined')\n\t? window   // if in browser\n\t: (\n\t\t(typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope)\n\t\t\t? self // if in worker\n\t\t\t: {}   // if in node js\n\t);\n\n/**\n * Prism: Lightweight, robust, elegant syntax highlighting\n *\n * @license MIT <https://opensource.org/licenses/MIT>\n * <AUTHOR> <PERSON> <https://lea.verou.me>\n * @namespace\n * @public\n */\nvar Prism = (function (_self) {\n\n\t// Private helper vars\n\tvar lang = /(?:^|\\s)lang(?:uage)?-([\\w-]+)(?=\\s|$)/i;\n\tvar uniqueId = 0;\n\n\t// The grammar object for plaintext\n\tvar plainTextGrammar = {};\n\n\n\tvar _ = {\n\t\t/**\n\t\t * By default, Prism will attempt to highlight all code elements (by calling {@link Prism.highlightAll}) on the\n\t\t * current page after the page finished loading. This might be a problem if e.g. you wanted to asynchronously load\n\t\t * additional languages or plugins yourself.\n\t\t *\n\t\t * By setting this value to `true`, Prism will not automatically highlight all code elements on the page.\n\t\t *\n\t\t * You obviously have to change this value before the automatic highlighting started. To do this, you can add an\n\t\t * empty Prism object into the global scope before loading the Prism script like this:\n\t\t *\n\t\t * ```js\n\t\t * window.Prism = window.Prism || {};\n\t\t * Prism.manual = true;\n\t\t * // add a new <script> to load Prism's script\n\t\t * ```\n\t\t *\n\t\t * @default false\n\t\t * @type {boolean}\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\tmanual: _self.Prism && _self.Prism.manual,\n\t\t/**\n\t\t * By default, if Prism is in a web worker, it assumes that it is in a worker it created itself, so it uses\n\t\t * `addEventListener` to communicate with its parent instance. However, if you're using Prism manually in your\n\t\t * own worker, you don't want it to do this.\n\t\t *\n\t\t * By setting this value to `true`, Prism will not add its own listeners to the worker.\n\t\t *\n\t\t * You obviously have to change this value before Prism executes. To do this, you can add an\n\t\t * empty Prism object into the global scope before loading the Prism script like this:\n\t\t *\n\t\t * ```js\n\t\t * window.Prism = window.Prism || {};\n\t\t * Prism.disableWorkerMessageHandler = true;\n\t\t * // Load Prism's script\n\t\t * ```\n\t\t *\n\t\t * @default false\n\t\t * @type {boolean}\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\tdisableWorkerMessageHandler: _self.Prism && _self.Prism.disableWorkerMessageHandler,\n\n\t\t/**\n\t\t * A namespace for utility methods.\n\t\t *\n\t\t * All function in this namespace that are not explicitly marked as _public_ are for __internal use only__ and may\n\t\t * change or disappear at any time.\n\t\t *\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t */\n\t\tutil: {\n\t\t\tencode: function encode(tokens) {\n\t\t\t\tif (tokens instanceof Token) {\n\t\t\t\t\treturn new Token(tokens.type, encode(tokens.content), tokens.alias);\n\t\t\t\t} else if (Array.isArray(tokens)) {\n\t\t\t\t\treturn tokens.map(encode);\n\t\t\t\t} else {\n\t\t\t\t\treturn tokens.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/\\u00a0/g, ' ');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the name of the type of the given value.\n\t\t\t *\n\t\t\t * @param {any} o\n\t\t\t * @returns {string}\n\t\t\t * @example\n\t\t\t * type(null)      === 'Null'\n\t\t\t * type(undefined) === 'Undefined'\n\t\t\t * type(123)       === 'Number'\n\t\t\t * type('foo')     === 'String'\n\t\t\t * type(true)      === 'Boolean'\n\t\t\t * type([1, 2])    === 'Array'\n\t\t\t * type({})        === 'Object'\n\t\t\t * type(String)    === 'Function'\n\t\t\t * type(/abc+/)    === 'RegExp'\n\t\t\t */\n\t\t\ttype: function (o) {\n\t\t\t\treturn Object.prototype.toString.call(o).slice(8, -1);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns a unique number for the given object. Later calls will still return the same number.\n\t\t\t *\n\t\t\t * @param {Object} obj\n\t\t\t * @returns {number}\n\t\t\t */\n\t\t\tobjId: function (obj) {\n\t\t\t\tif (!obj['__id']) {\n\t\t\t\t\tObject.defineProperty(obj, '__id', { value: ++uniqueId });\n\t\t\t\t}\n\t\t\t\treturn obj['__id'];\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Creates a deep clone of the given object.\n\t\t\t *\n\t\t\t * The main intended use of this function is to clone language definitions.\n\t\t\t *\n\t\t\t * @param {T} o\n\t\t\t * @param {Record<number, any>} [visited]\n\t\t\t * @returns {T}\n\t\t\t * @template T\n\t\t\t */\n\t\t\tclone: function deepClone(o, visited) {\n\t\t\t\tvisited = visited || {};\n\n\t\t\t\tvar clone; var id;\n\t\t\t\tswitch (_.util.type(o)) {\n\t\t\t\t\tcase 'Object':\n\t\t\t\t\t\tid = _.util.objId(o);\n\t\t\t\t\t\tif (visited[id]) {\n\t\t\t\t\t\t\treturn visited[id];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tclone = /** @type {Record<string, any>} */ ({});\n\t\t\t\t\t\tvisited[id] = clone;\n\n\t\t\t\t\t\tfor (var key in o) {\n\t\t\t\t\t\t\tif (o.hasOwnProperty(key)) {\n\t\t\t\t\t\t\t\tclone[key] = deepClone(o[key], visited);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn /** @type {any} */ (clone);\n\n\t\t\t\t\tcase 'Array':\n\t\t\t\t\t\tid = _.util.objId(o);\n\t\t\t\t\t\tif (visited[id]) {\n\t\t\t\t\t\t\treturn visited[id];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tclone = [];\n\t\t\t\t\t\tvisited[id] = clone;\n\n\t\t\t\t\t\t(/** @type {Array} */(/** @type {any} */(o))).forEach(function (v, i) {\n\t\t\t\t\t\t\tclone[i] = deepClone(v, visited);\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treturn /** @type {any} */ (clone);\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn o;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the Prism language of the given element set by a `language-xxxx` or `lang-xxxx` class.\n\t\t\t *\n\t\t\t * If no language is set for the element or the element is `null` or `undefined`, `none` will be returned.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @returns {string}\n\t\t\t */\n\t\t\tgetLanguage: function (element) {\n\t\t\t\twhile (element) {\n\t\t\t\t\tvar m = lang.exec(element.className);\n\t\t\t\t\tif (m) {\n\t\t\t\t\t\treturn m[1].toLowerCase();\n\t\t\t\t\t}\n\t\t\t\t\telement = element.parentElement;\n\t\t\t\t}\n\t\t\t\treturn 'none';\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Sets the Prism `language-xxxx` class of the given element.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @param {string} language\n\t\t\t * @returns {void}\n\t\t\t */\n\t\t\tsetLanguage: function (element, language) {\n\t\t\t\t// remove all `language-xxxx` classes\n\t\t\t\t// (this might leave behind a leading space)\n\t\t\t\telement.className = element.className.replace(RegExp(lang, 'gi'), '');\n\n\t\t\t\t// add the new `language-xxxx` class\n\t\t\t\t// (using `classList` will automatically clean up spaces for us)\n\t\t\t\telement.classList.add('language-' + language);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the script element that is currently executing.\n\t\t\t *\n\t\t\t * This does __not__ work for line script element.\n\t\t\t *\n\t\t\t * @returns {HTMLScriptElement | null}\n\t\t\t */\n\t\t\tcurrentScript: function () {\n\t\t\t\tif (typeof document === 'undefined') {\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t\tif ('currentScript' in document && 1 < 2 /* hack to trip TS' flow analysis */) {\n\t\t\t\t\treturn /** @type {any} */ (document.currentScript);\n\t\t\t\t}\n\n\t\t\t\t// IE11 workaround\n\t\t\t\t// we'll get the src of the current script by parsing IE11's error stack trace\n\t\t\t\t// this will not work for inline scripts\n\n\t\t\t\ttry {\n\t\t\t\t\tthrow new Error();\n\t\t\t\t} catch (err) {\n\t\t\t\t\t// Get file src url from stack. Specifically works with the format of stack traces in IE.\n\t\t\t\t\t// A stack will look like this:\n\t\t\t\t\t//\n\t\t\t\t\t// Error\n\t\t\t\t\t//    at _.util.currentScript (http://localhost/components/prism-core.js:119:5)\n\t\t\t\t\t//    at Global code (http://localhost/components/prism-core.js:606:1)\n\n\t\t\t\t\tvar src = (/at [^(\\r\\n]*\\((.*):[^:]+:[^:]+\\)$/i.exec(err.stack) || [])[1];\n\t\t\t\t\tif (src) {\n\t\t\t\t\t\tvar scripts = document.getElementsByTagName('script');\n\t\t\t\t\t\tfor (var i in scripts) {\n\t\t\t\t\t\t\tif (scripts[i].src == src) {\n\t\t\t\t\t\t\t\treturn scripts[i];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns whether a given class is active for `element`.\n\t\t\t *\n\t\t\t * The class can be activated if `element` or one of its ancestors has the given class and it can be deactivated\n\t\t\t * if `element` or one of its ancestors has the negated version of the given class. The _negated version_ of the\n\t\t\t * given class is just the given class with a `no-` prefix.\n\t\t\t *\n\t\t\t * Whether the class is active is determined by the closest ancestor of `element` (where `element` itself is\n\t\t\t * closest ancestor) that has the given class or the negated version of it. If neither `element` nor any of its\n\t\t\t * ancestors have the given class or the negated version of it, then the default activation will be returned.\n\t\t\t *\n\t\t\t * In the paradoxical situation where the closest ancestor contains __both__ the given class and the negated\n\t\t\t * version of it, the class is considered active.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @param {string} className\n\t\t\t * @param {boolean} [defaultActivation=false]\n\t\t\t * @returns {boolean}\n\t\t\t */\n\t\t\tisActive: function (element, className, defaultActivation) {\n\t\t\t\tvar no = 'no-' + className;\n\n\t\t\t\twhile (element) {\n\t\t\t\t\tvar classList = element.classList;\n\t\t\t\t\tif (classList.contains(className)) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t\tif (classList.contains(no)) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\telement = element.parentElement;\n\t\t\t\t}\n\t\t\t\treturn !!defaultActivation;\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * This namespace contains all currently loaded languages and the some helper functions to create and modify languages.\n\t\t *\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\tlanguages: {\n\t\t\t/**\n\t\t\t * The grammar for plain, unformatted text.\n\t\t\t */\n\t\t\tplain: plainTextGrammar,\n\t\t\tplaintext: plainTextGrammar,\n\t\t\ttext: plainTextGrammar,\n\t\t\ttxt: plainTextGrammar,\n\n\t\t\t/**\n\t\t\t * Creates a deep copy of the language with the given id and appends the given tokens.\n\t\t\t *\n\t\t\t * If a token in `redef` also appears in the copied language, then the existing token in the copied language\n\t\t\t * will be overwritten at its original position.\n\t\t\t *\n\t\t\t * ## Best practices\n\t\t\t *\n\t\t\t * Since the position of overwriting tokens (token in `redef` that overwrite tokens in the copied language)\n\t\t\t * doesn't matter, they can technically be in any order. However, this can be confusing to others that trying to\n\t\t\t * understand the language definition because, normally, the order of tokens matters in Prism grammars.\n\t\t\t *\n\t\t\t * Therefore, it is encouraged to order overwriting tokens according to the positions of the overwritten tokens.\n\t\t\t * Furthermore, all non-overwriting tokens should be placed after the overwriting ones.\n\t\t\t *\n\t\t\t * @param {string} id The id of the language to extend. This has to be a key in `Prism.languages`.\n\t\t\t * @param {Grammar} redef The new tokens to append.\n\t\t\t * @returns {Grammar} The new language created.\n\t\t\t * @public\n\t\t\t * @example\n\t\t\t * Prism.languages['css-with-colors'] = Prism.languages.extend('css', {\n\t\t\t *     // Prism.languages.css already has a 'comment' token, so this token will overwrite CSS' 'comment' token\n\t\t\t *     // at its original position\n\t\t\t *     'comment': { ... },\n\t\t\t *     // CSS doesn't have a 'color' token, so this token will be appended\n\t\t\t *     'color': /\\b(?:red|green|blue)\\b/\n\t\t\t * });\n\t\t\t */\n\t\t\textend: function (id, redef) {\n\t\t\t\tvar lang = _.util.clone(_.languages[id]);\n\n\t\t\t\tfor (var key in redef) {\n\t\t\t\t\tlang[key] = redef[key];\n\t\t\t\t}\n\n\t\t\t\treturn lang;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Inserts tokens _before_ another token in a language definition or any other grammar.\n\t\t\t *\n\t\t\t * ## Usage\n\t\t\t *\n\t\t\t * This helper method makes it easy to modify existing languages. For example, the CSS language definition\n\t\t\t * not only defines CSS highlighting for CSS documents, but also needs to define highlighting for CSS embedded\n\t\t\t * in HTML through `<style>` elements. To do this, it needs to modify `Prism.languages.markup` and add the\n\t\t\t * appropriate tokens. However, `Prism.languages.markup` is a regular JavaScript object literal, so if you do\n\t\t\t * this:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.markup.style = {\n\t\t\t *     // token\n\t\t\t * };\n\t\t\t * ```\n\t\t\t *\n\t\t\t * then the `style` token will be added (and processed) at the end. `insertBefore` allows you to insert tokens\n\t\t\t * before existing tokens. For the CSS example above, you would use it like this:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.insertBefore('markup', 'cdata', {\n\t\t\t *     'style': {\n\t\t\t *         // token\n\t\t\t *     }\n\t\t\t * });\n\t\t\t * ```\n\t\t\t *\n\t\t\t * ## Special cases\n\t\t\t *\n\t\t\t * If the grammars of `inside` and `insert` have tokens with the same name, the tokens in `inside`'s grammar\n\t\t\t * will be ignored.\n\t\t\t *\n\t\t\t * This behavior can be used to insert tokens after `before`:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.insertBefore('markup', 'comment', {\n\t\t\t *     'comment': Prism.languages.markup.comment,\n\t\t\t *     // tokens after 'comment'\n\t\t\t * });\n\t\t\t * ```\n\t\t\t *\n\t\t\t * ## Limitations\n\t\t\t *\n\t\t\t * The main problem `insertBefore` has to solve is iteration order. Since ES2015, the iteration order for object\n\t\t\t * properties is guaranteed to be the insertion order (except for integer keys) but some browsers behave\n\t\t\t * differently when keys are deleted and re-inserted. So `insertBefore` can't be implemented by temporarily\n\t\t\t * deleting properties which is necessary to insert at arbitrary positions.\n\t\t\t *\n\t\t\t * To solve this problem, `insertBefore` doesn't actually insert the given tokens into the target object.\n\t\t\t * Instead, it will create a new object and replace all references to the target object with the new one. This\n\t\t\t * can be done without temporarily deleting properties, so the iteration order is well-defined.\n\t\t\t *\n\t\t\t * However, only references that can be reached from `Prism.languages` or `insert` will be replaced. I.e. if\n\t\t\t * you hold the target object in a variable, then the value of the variable will not change.\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * var oldMarkup = Prism.languages.markup;\n\t\t\t * var newMarkup = Prism.languages.insertBefore('markup', 'comment', { ... });\n\t\t\t *\n\t\t\t * assert(oldMarkup !== Prism.languages.markup);\n\t\t\t * assert(newMarkup === Prism.languages.markup);\n\t\t\t * ```\n\t\t\t *\n\t\t\t * @param {string} inside The property of `root` (e.g. a language id in `Prism.languages`) that contains the\n\t\t\t * object to be modified.\n\t\t\t * @param {string} before The key to insert before.\n\t\t\t * @param {Grammar} insert An object containing the key-value pairs to be inserted.\n\t\t\t * @param {Object<string, any>} [root] The object containing `inside`, i.e. the object that contains the\n\t\t\t * object to be modified.\n\t\t\t *\n\t\t\t * Defaults to `Prism.languages`.\n\t\t\t * @returns {Grammar} The new grammar object.\n\t\t\t * @public\n\t\t\t */\n\t\t\tinsertBefore: function (inside, before, insert, root) {\n\t\t\t\troot = root || /** @type {any} */ (_.languages);\n\t\t\t\tvar grammar = root[inside];\n\t\t\t\t/** @type {Grammar} */\n\t\t\t\tvar ret = {};\n\n\t\t\t\tfor (var token in grammar) {\n\t\t\t\t\tif (grammar.hasOwnProperty(token)) {\n\n\t\t\t\t\t\tif (token == before) {\n\t\t\t\t\t\t\tfor (var newToken in insert) {\n\t\t\t\t\t\t\t\tif (insert.hasOwnProperty(newToken)) {\n\t\t\t\t\t\t\t\t\tret[newToken] = insert[newToken];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Do not insert token which also occur in insert. See #1525\n\t\t\t\t\t\tif (!insert.hasOwnProperty(token)) {\n\t\t\t\t\t\t\tret[token] = grammar[token];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tvar old = root[inside];\n\t\t\t\troot[inside] = ret;\n\n\t\t\t\t// Update references in other language definitions\n\t\t\t\t_.languages.DFS(_.languages, function (key, value) {\n\t\t\t\t\tif (value === old && key != inside) {\n\t\t\t\t\t\tthis[key] = ret;\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\treturn ret;\n\t\t\t},\n\n\t\t\t// Traverse a language definition with Depth First Search\n\t\t\tDFS: function DFS(o, callback, type, visited) {\n\t\t\t\tvisited = visited || {};\n\n\t\t\t\tvar objId = _.util.objId;\n\n\t\t\t\tfor (var i in o) {\n\t\t\t\t\tif (o.hasOwnProperty(i)) {\n\t\t\t\t\t\tcallback.call(o, i, o[i], type || i);\n\n\t\t\t\t\t\tvar property = o[i];\n\t\t\t\t\t\tvar propertyType = _.util.type(property);\n\n\t\t\t\t\t\tif (propertyType === 'Object' && !visited[objId(property)]) {\n\t\t\t\t\t\t\tvisited[objId(property)] = true;\n\t\t\t\t\t\t\tDFS(property, callback, null, visited);\n\t\t\t\t\t\t} else if (propertyType === 'Array' && !visited[objId(property)]) {\n\t\t\t\t\t\t\tvisited[objId(property)] = true;\n\t\t\t\t\t\t\tDFS(property, callback, i, visited);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tplugins: {},\n\n\t\t/**\n\t\t * This is the most high-level function in Prism’s API.\n\t\t * It fetches all the elements that have a `.language-xxxx` class and then calls {@link Prism.highlightElement} on\n\t\t * each one of them.\n\t\t *\n\t\t * This is equivalent to `Prism.highlightAllUnder(document, async, callback)`.\n\t\t *\n\t\t * @param {boolean} [async=false] Same as in {@link Prism.highlightAllUnder}.\n\t\t * @param {HighlightCallback} [callback] Same as in {@link Prism.highlightAllUnder}.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thighlightAll: function (async, callback) {\n\t\t\t_.highlightAllUnder(document, async, callback);\n\t\t},\n\n\t\t/**\n\t\t * Fetches all the descendants of `container` that have a `.language-xxxx` class and then calls\n\t\t * {@link Prism.highlightElement} on each one of them.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-highlightall`\n\t\t * 2. `before-all-elements-highlight`\n\t\t * 3. All hooks of {@link Prism.highlightElement} for each element.\n\t\t *\n\t\t * @param {ParentNode} container The root element, whose descendants that have a `.language-xxxx` class will be highlighted.\n\t\t * @param {boolean} [async=false] Whether each element is to be highlighted asynchronously using Web Workers.\n\t\t * @param {HighlightCallback} [callback] An optional callback to be invoked on each element after its highlighting is done.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thighlightAllUnder: function (container, async, callback) {\n\t\t\tvar env = {\n\t\t\t\tcallback: callback,\n\t\t\t\tcontainer: container,\n\t\t\t\tselector: 'code[class*=\"language-\"], [class*=\"language-\"] code, code[class*=\"lang-\"], [class*=\"lang-\"] code'\n\t\t\t};\n\n\t\t\t_.hooks.run('before-highlightall', env);\n\n\t\t\tenv.elements = Array.prototype.slice.apply(env.container.querySelectorAll(env.selector));\n\n\t\t\t_.hooks.run('before-all-elements-highlight', env);\n\n\t\t\tfor (var i = 0, element; (element = env.elements[i++]);) {\n\t\t\t\t_.highlightElement(element, async === true, env.callback);\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * Highlights the code inside a single element.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-sanity-check`\n\t\t * 2. `before-highlight`\n\t\t * 3. All hooks of {@link Prism.highlight}. These hooks will be run by an asynchronous worker if `async` is `true`.\n\t\t * 4. `before-insert`\n\t\t * 5. `after-highlight`\n\t\t * 6. `complete`\n\t\t *\n\t\t * Some the above hooks will be skipped if the element doesn't contain any text or there is no grammar loaded for\n\t\t * the element's language.\n\t\t *\n\t\t * @param {Element} element The element containing the code.\n\t\t * It must have a class of `language-xxxx` to be processed, where `xxxx` is a valid language identifier.\n\t\t * @param {boolean} [async=false] Whether the element is to be highlighted asynchronously using Web Workers\n\t\t * to improve performance and avoid blocking the UI when highlighting very large chunks of code. This option is\n\t\t * [disabled by default](https://prismjs.com/faq.html#why-is-asynchronous-highlighting-disabled-by-default).\n\t\t *\n\t\t * Note: All language definitions required to highlight the code must be included in the main `prism.js` file for\n\t\t * asynchronous highlighting to work. You can build your own bundle on the\n\t\t * [Download page](https://prismjs.com/download.html).\n\t\t * @param {HighlightCallback} [callback] An optional callback to be invoked after the highlighting is done.\n\t\t * Mostly useful when `async` is `true`, since in that case, the highlighting is done asynchronously.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thighlightElement: function (element, async, callback) {\n\t\t\t// Find language\n\t\t\tvar language = _.util.getLanguage(element);\n\t\t\tvar grammar = _.languages[language];\n\n\t\t\t// Set language on the element, if not present\n\t\t\t_.util.setLanguage(element, language);\n\n\t\t\t// Set language on the parent, for styling\n\t\t\tvar parent = element.parentElement;\n\t\t\tif (parent && parent.nodeName.toLowerCase() === 'pre') {\n\t\t\t\t_.util.setLanguage(parent, language);\n\t\t\t}\n\n\t\t\tvar code = element.textContent;\n\n\t\t\tvar env = {\n\t\t\t\telement: element,\n\t\t\t\tlanguage: language,\n\t\t\t\tgrammar: grammar,\n\t\t\t\tcode: code\n\t\t\t};\n\n\t\t\tfunction insertHighlightedCode(highlightedCode) {\n\t\t\t\tenv.highlightedCode = highlightedCode;\n\n\t\t\t\t_.hooks.run('before-insert', env);\n\n\t\t\t\tenv.element.innerHTML = env.highlightedCode;\n\n\t\t\t\t_.hooks.run('after-highlight', env);\n\t\t\t\t_.hooks.run('complete', env);\n\t\t\t\tcallback && callback.call(env.element);\n\t\t\t}\n\n\t\t\t_.hooks.run('before-sanity-check', env);\n\n\t\t\t// plugins may change/add the parent/element\n\t\t\tparent = env.element.parentElement;\n\t\t\tif (parent && parent.nodeName.toLowerCase() === 'pre' && !parent.hasAttribute('tabindex')) {\n\t\t\t\tparent.setAttribute('tabindex', '0');\n\t\t\t}\n\n\t\t\tif (!env.code) {\n\t\t\t\t_.hooks.run('complete', env);\n\t\t\t\tcallback && callback.call(env.element);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t_.hooks.run('before-highlight', env);\n\n\t\t\tif (!env.grammar) {\n\t\t\t\tinsertHighlightedCode(_.util.encode(env.code));\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (async && _self.Worker) {\n\t\t\t\tvar worker = new Worker(_.filename);\n\n\t\t\t\tworker.onmessage = function (evt) {\n\t\t\t\t\tinsertHighlightedCode(evt.data);\n\t\t\t\t};\n\n\t\t\t\tworker.postMessage(JSON.stringify({\n\t\t\t\t\tlanguage: env.language,\n\t\t\t\t\tcode: env.code,\n\t\t\t\t\timmediateClose: true\n\t\t\t\t}));\n\t\t\t} else {\n\t\t\t\tinsertHighlightedCode(_.highlight(env.code, env.grammar, env.language));\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * Low-level function, only use if you know what you’re doing. It accepts a string of text as input\n\t\t * and the language definitions to use, and returns a string with the HTML produced.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-tokenize`\n\t\t * 2. `after-tokenize`\n\t\t * 3. `wrap`: On each {@link Token}.\n\t\t *\n\t\t * @param {string} text A string with the code to be highlighted.\n\t\t * @param {Grammar} grammar An object containing the tokens to use.\n\t\t *\n\t\t * Usually a language definition like `Prism.languages.markup`.\n\t\t * @param {string} language The name of the language definition passed to `grammar`.\n\t\t * @returns {string} The highlighted HTML.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t * @example\n\t\t * Prism.highlight('var foo = true;', Prism.languages.javascript, 'javascript');\n\t\t */\n\t\thighlight: function (text, grammar, language) {\n\t\t\tvar env = {\n\t\t\t\tcode: text,\n\t\t\t\tgrammar: grammar,\n\t\t\t\tlanguage: language\n\t\t\t};\n\t\t\t_.hooks.run('before-tokenize', env);\n\t\t\tif (!env.grammar) {\n\t\t\t\tthrow new Error('The language \"' + env.language + '\" has no grammar.');\n\t\t\t}\n\t\t\tenv.tokens = _.tokenize(env.code, env.grammar);\n\t\t\t_.hooks.run('after-tokenize', env);\n\t\t\treturn Token.stringify(_.util.encode(env.tokens), env.language);\n\t\t},\n\n\t\t/**\n\t\t * This is the heart of Prism, and the most low-level function you can use. It accepts a string of text as input\n\t\t * and the language definitions to use, and returns an array with the tokenized code.\n\t\t *\n\t\t * When the language definition includes nested tokens, the function is called recursively on each of these tokens.\n\t\t *\n\t\t * This method could be useful in other contexts as well, as a very crude parser.\n\t\t *\n\t\t * @param {string} text A string with the code to be highlighted.\n\t\t * @param {Grammar} grammar An object containing the tokens to use.\n\t\t *\n\t\t * Usually a language definition like `Prism.languages.markup`.\n\t\t * @returns {TokenStream} An array of strings and tokens, a token stream.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t * @example\n\t\t * let code = `var foo = 0;`;\n\t\t * let tokens = Prism.tokenize(code, Prism.languages.javascript);\n\t\t * tokens.forEach(token => {\n\t\t *     if (token instanceof Prism.Token && token.type === 'number') {\n\t\t *         console.log(`Found numeric literal: ${token.content}`);\n\t\t *     }\n\t\t * });\n\t\t */\n\t\ttokenize: function (text, grammar) {\n\t\t\tvar rest = grammar.rest;\n\t\t\tif (rest) {\n\t\t\t\tfor (var token in rest) {\n\t\t\t\t\tgrammar[token] = rest[token];\n\t\t\t\t}\n\n\t\t\t\tdelete grammar.rest;\n\t\t\t}\n\n\t\t\tvar tokenList = new LinkedList();\n\t\t\taddAfter(tokenList, tokenList.head, text);\n\n\t\t\tmatchGrammar(text, tokenList, grammar, tokenList.head, 0);\n\n\t\t\treturn toArray(tokenList);\n\t\t},\n\n\t\t/**\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thooks: {\n\t\t\tall: {},\n\n\t\t\t/**\n\t\t\t * Adds the given callback to the list of callbacks for the given hook.\n\t\t\t *\n\t\t\t * The callback will be invoked when the hook it is registered for is run.\n\t\t\t * Hooks are usually directly run by a highlight function but you can also run hooks yourself.\n\t\t\t *\n\t\t\t * One callback function can be registered to multiple hooks and the same hook multiple times.\n\t\t\t *\n\t\t\t * @param {string} name The name of the hook.\n\t\t\t * @param {HookCallback} callback The callback function which is given environment variables.\n\t\t\t * @public\n\t\t\t */\n\t\t\tadd: function (name, callback) {\n\t\t\t\tvar hooks = _.hooks.all;\n\n\t\t\t\thooks[name] = hooks[name] || [];\n\n\t\t\t\thooks[name].push(callback);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Runs a hook invoking all registered callbacks with the given environment variables.\n\t\t\t *\n\t\t\t * Callbacks will be invoked synchronously and in the order in which they were registered.\n\t\t\t *\n\t\t\t * @param {string} name The name of the hook.\n\t\t\t * @param {Object<string, any>} env The environment variables of the hook passed to all callbacks registered.\n\t\t\t * @public\n\t\t\t */\n\t\t\trun: function (name, env) {\n\t\t\t\tvar callbacks = _.hooks.all[name];\n\n\t\t\t\tif (!callbacks || !callbacks.length) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tfor (var i = 0, callback; (callback = callbacks[i++]);) {\n\t\t\t\t\tcallback(env);\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tToken: Token\n\t};\n\t_self.Prism = _;\n\n\n\t// Typescript note:\n\t// The following can be used to import the Token type in JSDoc:\n\t//\n\t//   @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token\n\n\t/**\n\t * Creates a new token.\n\t *\n\t * @param {string} type See {@link Token#type type}\n\t * @param {string | TokenStream} content See {@link Token#content content}\n\t * @param {string|string[]} [alias] The alias(es) of the token.\n\t * @param {string} [matchedStr=\"\"] A copy of the full string this token was created from.\n\t * @class\n\t * @global\n\t * @public\n\t */\n\tfunction Token(type, content, alias, matchedStr) {\n\t\t/**\n\t\t * The type of the token.\n\t\t *\n\t\t * This is usually the key of a pattern in a {@link Grammar}.\n\t\t *\n\t\t * @type {string}\n\t\t * @see GrammarToken\n\t\t * @public\n\t\t */\n\t\tthis.type = type;\n\t\t/**\n\t\t * The strings or tokens contained by this token.\n\t\t *\n\t\t * This will be a token stream if the pattern matched also defined an `inside` grammar.\n\t\t *\n\t\t * @type {string | TokenStream}\n\t\t * @public\n\t\t */\n\t\tthis.content = content;\n\t\t/**\n\t\t * The alias(es) of the token.\n\t\t *\n\t\t * @type {string|string[]}\n\t\t * @see GrammarToken\n\t\t * @public\n\t\t */\n\t\tthis.alias = alias;\n\t\t// Copy of the full string this token was created from\n\t\tthis.length = (matchedStr || '').length | 0;\n\t}\n\n\t/**\n\t * A token stream is an array of strings and {@link Token Token} objects.\n\t *\n\t * Token streams have to fulfill a few properties that are assumed by most functions (mostly internal ones) that process\n\t * them.\n\t *\n\t * 1. No adjacent strings.\n\t * 2. No empty strings.\n\t *\n\t *    The only exception here is the token stream that only contains the empty string and nothing else.\n\t *\n\t * @typedef {Array<string | Token>} TokenStream\n\t * @global\n\t * @public\n\t */\n\n\t/**\n\t * Converts the given token or token stream to an HTML representation.\n\t *\n\t * The following hooks will be run:\n\t * 1. `wrap`: On each {@link Token}.\n\t *\n\t * @param {string | Token | TokenStream} o The token or token stream to be converted.\n\t * @param {string} language The name of current language.\n\t * @returns {string} The HTML representation of the token or token stream.\n\t * @memberof Token\n\t * @static\n\t */\n\tToken.stringify = function stringify(o, language) {\n\t\tif (typeof o == 'string') {\n\t\t\treturn o;\n\t\t}\n\t\tif (Array.isArray(o)) {\n\t\t\tvar s = '';\n\t\t\to.forEach(function (e) {\n\t\t\t\ts += stringify(e, language);\n\t\t\t});\n\t\t\treturn s;\n\t\t}\n\n\t\tvar env = {\n\t\t\ttype: o.type,\n\t\t\tcontent: stringify(o.content, language),\n\t\t\ttag: 'span',\n\t\t\tclasses: ['token', o.type],\n\t\t\tattributes: {},\n\t\t\tlanguage: language\n\t\t};\n\n\t\tvar aliases = o.alias;\n\t\tif (aliases) {\n\t\t\tif (Array.isArray(aliases)) {\n\t\t\t\tArray.prototype.push.apply(env.classes, aliases);\n\t\t\t} else {\n\t\t\t\tenv.classes.push(aliases);\n\t\t\t}\n\t\t}\n\n\t\t_.hooks.run('wrap', env);\n\n\t\tvar attributes = '';\n\t\tfor (var name in env.attributes) {\n\t\t\tattributes += ' ' + name + '=\"' + (env.attributes[name] || '').replace(/\"/g, '&quot;') + '\"';\n\t\t}\n\n\t\treturn '<' + env.tag + ' class=\"' + env.classes.join(' ') + '\"' + attributes + '>' + env.content + '</' + env.tag + '>';\n\t};\n\n\t/**\n\t * @param {RegExp} pattern\n\t * @param {number} pos\n\t * @param {string} text\n\t * @param {boolean} lookbehind\n\t * @returns {RegExpExecArray | null}\n\t */\n\tfunction matchPattern(pattern, pos, text, lookbehind) {\n\t\tpattern.lastIndex = pos;\n\t\tvar match = pattern.exec(text);\n\t\tif (match && lookbehind && match[1]) {\n\t\t\t// change the match to remove the text matched by the Prism lookbehind group\n\t\t\tvar lookbehindLength = match[1].length;\n\t\t\tmatch.index += lookbehindLength;\n\t\t\tmatch[0] = match[0].slice(lookbehindLength);\n\t\t}\n\t\treturn match;\n\t}\n\n\t/**\n\t * @param {string} text\n\t * @param {LinkedList<string | Token>} tokenList\n\t * @param {any} grammar\n\t * @param {LinkedListNode<string | Token>} startNode\n\t * @param {number} startPos\n\t * @param {RematchOptions} [rematch]\n\t * @returns {void}\n\t * @private\n\t *\n\t * @typedef RematchOptions\n\t * @property {string} cause\n\t * @property {number} reach\n\t */\n\tfunction matchGrammar(text, tokenList, grammar, startNode, startPos, rematch) {\n\t\tfor (var token in grammar) {\n\t\t\tif (!grammar.hasOwnProperty(token) || !grammar[token]) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tvar patterns = grammar[token];\n\t\t\tpatterns = Array.isArray(patterns) ? patterns : [patterns];\n\n\t\t\tfor (var j = 0; j < patterns.length; ++j) {\n\t\t\t\tif (rematch && rematch.cause == token + ',' + j) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar patternObj = patterns[j];\n\t\t\t\tvar inside = patternObj.inside;\n\t\t\t\tvar lookbehind = !!patternObj.lookbehind;\n\t\t\t\tvar greedy = !!patternObj.greedy;\n\t\t\t\tvar alias = patternObj.alias;\n\n\t\t\t\tif (greedy && !patternObj.pattern.global) {\n\t\t\t\t\t// Without the global flag, lastIndex won't work\n\t\t\t\t\tvar flags = patternObj.pattern.toString().match(/[imsuy]*$/)[0];\n\t\t\t\t\tpatternObj.pattern = RegExp(patternObj.pattern.source, flags + 'g');\n\t\t\t\t}\n\n\t\t\t\t/** @type {RegExp} */\n\t\t\t\tvar pattern = patternObj.pattern || patternObj;\n\n\t\t\t\tfor ( // iterate the token list and keep track of the current token/string position\n\t\t\t\t\tvar currentNode = startNode.next, pos = startPos;\n\t\t\t\t\tcurrentNode !== tokenList.tail;\n\t\t\t\t\tpos += currentNode.value.length, currentNode = currentNode.next\n\t\t\t\t) {\n\n\t\t\t\t\tif (rematch && pos >= rematch.reach) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar str = currentNode.value;\n\n\t\t\t\t\tif (tokenList.length > text.length) {\n\t\t\t\t\t\t// Something went terribly wrong, ABORT, ABORT!\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (str instanceof Token) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar removeCount = 1; // this is the to parameter of removeBetween\n\t\t\t\t\tvar match;\n\n\t\t\t\t\tif (greedy) {\n\t\t\t\t\t\tmatch = matchPattern(pattern, pos, text, lookbehind);\n\t\t\t\t\t\tif (!match || match.index >= text.length) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tvar from = match.index;\n\t\t\t\t\t\tvar to = match.index + match[0].length;\n\t\t\t\t\t\tvar p = pos;\n\n\t\t\t\t\t\t// find the node that contains the match\n\t\t\t\t\t\tp += currentNode.value.length;\n\t\t\t\t\t\twhile (from >= p) {\n\t\t\t\t\t\t\tcurrentNode = currentNode.next;\n\t\t\t\t\t\t\tp += currentNode.value.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// adjust pos (and p)\n\t\t\t\t\t\tp -= currentNode.value.length;\n\t\t\t\t\t\tpos = p;\n\n\t\t\t\t\t\t// the current node is a Token, then the match starts inside another Token, which is invalid\n\t\t\t\t\t\tif (currentNode.value instanceof Token) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// find the last node which is affected by this match\n\t\t\t\t\t\tfor (\n\t\t\t\t\t\t\tvar k = currentNode;\n\t\t\t\t\t\t\tk !== tokenList.tail && (p < to || typeof k.value === 'string');\n\t\t\t\t\t\t\tk = k.next\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tremoveCount++;\n\t\t\t\t\t\t\tp += k.value.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tremoveCount--;\n\n\t\t\t\t\t\t// replace with the new match\n\t\t\t\t\t\tstr = text.slice(pos, p);\n\t\t\t\t\t\tmatch.index -= pos;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmatch = matchPattern(pattern, 0, str, lookbehind);\n\t\t\t\t\t\tif (!match) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// eslint-disable-next-line no-redeclare\n\t\t\t\t\tvar from = match.index;\n\t\t\t\t\tvar matchStr = match[0];\n\t\t\t\t\tvar before = str.slice(0, from);\n\t\t\t\t\tvar after = str.slice(from + matchStr.length);\n\n\t\t\t\t\tvar reach = pos + str.length;\n\t\t\t\t\tif (rematch && reach > rematch.reach) {\n\t\t\t\t\t\trematch.reach = reach;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar removeFrom = currentNode.prev;\n\n\t\t\t\t\tif (before) {\n\t\t\t\t\t\tremoveFrom = addAfter(tokenList, removeFrom, before);\n\t\t\t\t\t\tpos += before.length;\n\t\t\t\t\t}\n\n\t\t\t\t\tremoveRange(tokenList, removeFrom, removeCount);\n\n\t\t\t\t\tvar wrapped = new Token(token, inside ? _.tokenize(matchStr, inside) : matchStr, alias, matchStr);\n\t\t\t\t\tcurrentNode = addAfter(tokenList, removeFrom, wrapped);\n\n\t\t\t\t\tif (after) {\n\t\t\t\t\t\taddAfter(tokenList, currentNode, after);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (removeCount > 1) {\n\t\t\t\t\t\t// at least one Token object was removed, so we have to do some rematching\n\t\t\t\t\t\t// this can only happen if the current pattern is greedy\n\n\t\t\t\t\t\t/** @type {RematchOptions} */\n\t\t\t\t\t\tvar nestedRematch = {\n\t\t\t\t\t\t\tcause: token + ',' + j,\n\t\t\t\t\t\t\treach: reach\n\t\t\t\t\t\t};\n\t\t\t\t\t\tmatchGrammar(text, tokenList, grammar, currentNode.prev, pos, nestedRematch);\n\n\t\t\t\t\t\t// the reach might have been extended because of the rematching\n\t\t\t\t\t\tif (rematch && nestedRematch.reach > rematch.reach) {\n\t\t\t\t\t\t\trematch.reach = nestedRematch.reach;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @typedef LinkedListNode\n\t * @property {T} value\n\t * @property {LinkedListNode<T> | null} prev The previous node.\n\t * @property {LinkedListNode<T> | null} next The next node.\n\t * @template T\n\t * @private\n\t */\n\n\t/**\n\t * @template T\n\t * @private\n\t */\n\tfunction LinkedList() {\n\t\t/** @type {LinkedListNode<T>} */\n\t\tvar head = { value: null, prev: null, next: null };\n\t\t/** @type {LinkedListNode<T>} */\n\t\tvar tail = { value: null, prev: head, next: null };\n\t\thead.next = tail;\n\n\t\t/** @type {LinkedListNode<T>} */\n\t\tthis.head = head;\n\t\t/** @type {LinkedListNode<T>} */\n\t\tthis.tail = tail;\n\t\tthis.length = 0;\n\t}\n\n\t/**\n\t * Adds a new node with the given value to the list.\n\t *\n\t * @param {LinkedList<T>} list\n\t * @param {LinkedListNode<T>} node\n\t * @param {T} value\n\t * @returns {LinkedListNode<T>} The added node.\n\t * @template T\n\t */\n\tfunction addAfter(list, node, value) {\n\t\t// assumes that node != list.tail && values.length >= 0\n\t\tvar next = node.next;\n\n\t\tvar newNode = { value: value, prev: node, next: next };\n\t\tnode.next = newNode;\n\t\tnext.prev = newNode;\n\t\tlist.length++;\n\n\t\treturn newNode;\n\t}\n\t/**\n\t * Removes `count` nodes after the given node. The given node will not be removed.\n\t *\n\t * @param {LinkedList<T>} list\n\t * @param {LinkedListNode<T>} node\n\t * @param {number} count\n\t * @template T\n\t */\n\tfunction removeRange(list, node, count) {\n\t\tvar next = node.next;\n\t\tfor (var i = 0; i < count && next !== list.tail; i++) {\n\t\t\tnext = next.next;\n\t\t}\n\t\tnode.next = next;\n\t\tnext.prev = node;\n\t\tlist.length -= i;\n\t}\n\t/**\n\t * @param {LinkedList<T>} list\n\t * @returns {T[]}\n\t * @template T\n\t */\n\tfunction toArray(list) {\n\t\tvar array = [];\n\t\tvar node = list.head.next;\n\t\twhile (node !== list.tail) {\n\t\t\tarray.push(node.value);\n\t\t\tnode = node.next;\n\t\t}\n\t\treturn array;\n\t}\n\n\n\tif (!_self.document) {\n\t\tif (!_self.addEventListener) {\n\t\t\t// in Node.js\n\t\t\treturn _;\n\t\t}\n\n\t\tif (!_.disableWorkerMessageHandler) {\n\t\t\t// In worker\n\t\t\t_self.addEventListener('message', function (evt) {\n\t\t\t\tvar message = JSON.parse(evt.data);\n\t\t\t\tvar lang = message.language;\n\t\t\t\tvar code = message.code;\n\t\t\t\tvar immediateClose = message.immediateClose;\n\n\t\t\t\t_self.postMessage(_.highlight(code, _.languages[lang], lang));\n\t\t\t\tif (immediateClose) {\n\t\t\t\t\t_self.close();\n\t\t\t\t}\n\t\t\t}, false);\n\t\t}\n\n\t\treturn _;\n\t}\n\n\t// Get current script and highlight\n\tvar script = _.util.currentScript();\n\n\tif (script) {\n\t\t_.filename = script.src;\n\n\t\tif (script.hasAttribute('data-manual')) {\n\t\t\t_.manual = true;\n\t\t}\n\t}\n\n\tfunction highlightAutomaticallyCallback() {\n\t\tif (!_.manual) {\n\t\t\t_.highlightAll();\n\t\t}\n\t}\n\n\tif (!_.manual) {\n\t\t// If the document state is \"loading\", then we'll use DOMContentLoaded.\n\t\t// If the document state is \"interactive\" and the prism.js script is deferred, then we'll also use the\n\t\t// DOMContentLoaded event because there might be some plugins or languages which have also been deferred and they\n\t\t// might take longer one animation frame to execute which can create a race condition where only some plugins have\n\t\t// been loaded when Prism.highlightAll() is executed, depending on how fast resources are loaded.\n\t\t// See https://github.com/PrismJS/prism/issues/2102\n\t\tvar readyState = document.readyState;\n\t\tif (readyState === 'loading' || readyState === 'interactive' && script && script.defer) {\n\t\t\tdocument.addEventListener('DOMContentLoaded', highlightAutomaticallyCallback);\n\t\t} else {\n\t\t\tif (window.requestAnimationFrame) {\n\t\t\t\twindow.requestAnimationFrame(highlightAutomaticallyCallback);\n\t\t\t} else {\n\t\t\t\twindow.setTimeout(highlightAutomaticallyCallback, 16);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn _;\n\n}(_self));\n\nif (typeof module !== 'undefined' && module.exports) {\n\tmodule.exports = Prism;\n}\n\n// hack for components to work correctly in node.js\nif (typeof global !== 'undefined') {\n\tglobal.Prism = Prism;\n}\n\n// some additional documentation/types\n\n/**\n * The expansion of a simple `RegExp` literal to support additional properties.\n *\n * @typedef GrammarToken\n * @property {RegExp} pattern The regular expression of the token.\n * @property {boolean} [lookbehind=false] If `true`, then the first capturing group of `pattern` will (effectively)\n * behave as a lookbehind group meaning that the captured text will not be part of the matched text of the new token.\n * @property {boolean} [greedy=false] Whether the token is greedy.\n * @property {string|string[]} [alias] An optional alias or list of aliases.\n * @property {Grammar} [inside] The nested grammar of this token.\n *\n * The `inside` grammar will be used to tokenize the text value of each token of this kind.\n *\n * This can be used to make nested and even recursive language definitions.\n *\n * Note: This can cause infinite recursion. Be careful when you embed different languages or even the same language into\n * each another.\n * @global\n * @public\n */\n\n/**\n * @typedef Grammar\n * @type {Object<string, RegExp | GrammarToken | Array<RegExp | GrammarToken>>}\n * @property {Grammar} [rest] An optional grammar object that will be appended to this grammar.\n * @global\n * @public\n */\n\n/**\n * A function which will invoked after an element was successfully highlighted.\n *\n * @callback HighlightCallback\n * @param {Element} element The element successfully highlighted.\n * @returns {void}\n * @global\n * @public\n */\n\n/**\n * @callback HookCallback\n * @param {Object<string, any>} env The environment variables of the hook.\n * @returns {void}\n * @global\n * @public\n */\n\n\n/* **********************************************\n     Begin prism-markup.js\n********************************************** */\n\nPrism.languages.markup = {\n\t'comment': {\n\t\tpattern: /<!--(?:(?!<!--)[\\s\\S])*?-->/,\n\t\tgreedy: true\n\t},\n\t'prolog': {\n\t\tpattern: /<\\?[\\s\\S]+?\\?>/,\n\t\tgreedy: true\n\t},\n\t'doctype': {\n\t\t// https://www.w3.org/TR/xml/#NT-doctypedecl\n\t\tpattern: /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'internal-subset': {\n\t\t\t\tpattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: null // see below\n\t\t\t},\n\t\t\t'string': {\n\t\t\t\tpattern: /\"[^\"]*\"|'[^']*'/,\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t'punctuation': /^<!|>$|[[\\]]/,\n\t\t\t'doctype-tag': /^DOCTYPE/i,\n\t\t\t'name': /[^\\s<>'\"]+/\n\t\t}\n\t},\n\t'cdata': {\n\t\tpattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n\t\tgreedy: true\n\t},\n\t'tag': {\n\t\tpattern: /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'tag': {\n\t\t\t\tpattern: /^<\\/?[^\\s>\\/]+/,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /^<\\/?/,\n\t\t\t\t\t'namespace': /^[^\\s>\\/:]+:/\n\t\t\t\t}\n\t\t\t},\n\t\t\t'special-attr': [],\n\t\t\t'attr-value': {\n\t\t\t\tpattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tpattern: /^=/,\n\t\t\t\t\t\t\talias: 'attr-equals'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tpattern: /^(\\s*)[\"']|[\"']$/,\n\t\t\t\t\t\t\tlookbehind: true\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t}\n\t\t\t},\n\t\t\t'punctuation': /\\/?>/,\n\t\t\t'attr-name': {\n\t\t\t\tpattern: /[^\\s>\\/]+/,\n\t\t\t\tinside: {\n\t\t\t\t\t'namespace': /^[^\\s>\\/:]+:/\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\t},\n\t'entity': [\n\t\t{\n\t\t\tpattern: /&[\\da-z]{1,8};/i,\n\t\t\talias: 'named-entity'\n\t\t},\n\t\t/&#x?[\\da-f]{1,8};/i\n\t]\n};\n\nPrism.languages.markup['tag'].inside['attr-value'].inside['entity'] =\n\tPrism.languages.markup['entity'];\nPrism.languages.markup['doctype'].inside['internal-subset'].inside = Prism.languages.markup;\n\n// Plugin to make entity title show the real entity, idea by Roman Komarov\nPrism.hooks.add('wrap', function (env) {\n\n\tif (env.type === 'entity') {\n\t\tenv.attributes['title'] = env.content.replace(/&amp;/, '&');\n\t}\n});\n\nObject.defineProperty(Prism.languages.markup.tag, 'addInlined', {\n\t/**\n\t * Adds an inlined language to markup.\n\t *\n\t * An example of an inlined language is CSS with `<style>` tags.\n\t *\n\t * @param {string} tagName The name of the tag that contains the inlined language. This name will be treated as\n\t * case insensitive.\n\t * @param {string} lang The language key.\n\t * @example\n\t * addInlined('style', 'css');\n\t */\n\tvalue: function addInlined(tagName, lang) {\n\t\tvar includedCdataInside = {};\n\t\tincludedCdataInside['language-' + lang] = {\n\t\t\tpattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages[lang]\n\t\t};\n\t\tincludedCdataInside['cdata'] = /^<!\\[CDATA\\[|\\]\\]>$/i;\n\n\t\tvar inside = {\n\t\t\t'included-cdata': {\n\t\t\t\tpattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n\t\t\t\tinside: includedCdataInside\n\t\t\t}\n\t\t};\n\t\tinside['language-' + lang] = {\n\t\t\tpattern: /[\\s\\S]+/,\n\t\t\tinside: Prism.languages[lang]\n\t\t};\n\n\t\tvar def = {};\n\t\tdef[tagName] = {\n\t\t\tpattern: RegExp(/(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(/__/g, function () { return tagName; }), 'i'),\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\tinside: inside\n\t\t};\n\n\t\tPrism.languages.insertBefore('markup', 'cdata', def);\n\t}\n});\nObject.defineProperty(Prism.languages.markup.tag, 'addAttribute', {\n\t/**\n\t * Adds an pattern to highlight languages embedded in HTML attributes.\n\t *\n\t * An example of an inlined language is CSS with `style` attributes.\n\t *\n\t * @param {string} attrName The name of the tag that contains the inlined language. This name will be treated as\n\t * case insensitive.\n\t * @param {string} lang The language key.\n\t * @example\n\t * addAttribute('style', 'css');\n\t */\n\tvalue: function (attrName, lang) {\n\t\tPrism.languages.markup.tag.inside['special-attr'].push({\n\t\t\tpattern: RegExp(\n\t\t\t\t/(^|[\"'\\s])/.source + '(?:' + attrName + ')' + /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source,\n\t\t\t\t'i'\n\t\t\t),\n\t\t\tlookbehind: true,\n\t\t\tinside: {\n\t\t\t\t'attr-name': /^[^\\s=]+/,\n\t\t\t\t'attr-value': {\n\t\t\t\t\tpattern: /=[\\s\\S]+/,\n\t\t\t\t\tinside: {\n\t\t\t\t\t\t'value': {\n\t\t\t\t\t\t\tpattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,\n\t\t\t\t\t\t\tlookbehind: true,\n\t\t\t\t\t\t\talias: [lang, 'language-' + lang],\n\t\t\t\t\t\t\tinside: Prism.languages[lang]\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'punctuation': [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tpattern: /^=/,\n\t\t\t\t\t\t\t\talias: 'attr-equals'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t/\"|'/\n\t\t\t\t\t\t]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n});\n\nPrism.languages.html = Prism.languages.markup;\nPrism.languages.mathml = Prism.languages.markup;\nPrism.languages.svg = Prism.languages.markup;\n\nPrism.languages.xml = Prism.languages.extend('markup', {});\nPrism.languages.ssml = Prism.languages.xml;\nPrism.languages.atom = Prism.languages.xml;\nPrism.languages.rss = Prism.languages.xml;\n\n\n/* **********************************************\n     Begin prism-css.js\n********************************************** */\n\n(function (Prism) {\n\n\tvar string = /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/;\n\n\tPrism.languages.css = {\n\t\t'comment': /\\/\\*[\\s\\S]*?\\*\\//,\n\t\t'atrule': {\n\t\t\tpattern: RegExp('@[\\\\w-](?:' + /[^;{\\s\"']|\\s+(?!\\s)/.source + '|' + string.source + ')*?' + /(?:;|(?=\\s*\\{))/.source),\n\t\t\tinside: {\n\t\t\t\t'rule': /^@[\\w-]+/,\n\t\t\t\t'selector-function-argument': {\n\t\t\t\t\tpattern: /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,\n\t\t\t\t\tlookbehind: true,\n\t\t\t\t\talias: 'selector'\n\t\t\t\t},\n\t\t\t\t'keyword': {\n\t\t\t\t\tpattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,\n\t\t\t\t\tlookbehind: true\n\t\t\t\t}\n\t\t\t\t// See rest below\n\t\t\t}\n\t\t},\n\t\t'url': {\n\t\t\t// https://drafts.csswg.org/css-values-3/#urls\n\t\t\tpattern: RegExp('\\\\burl\\\\((?:' + string.source + '|' + /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source + ')\\\\)', 'i'),\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'function': /^url/i,\n\t\t\t\t'punctuation': /^\\(|\\)$/,\n\t\t\t\t'string': {\n\t\t\t\t\tpattern: RegExp('^' + string.source + '$'),\n\t\t\t\t\talias: 'url'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t'selector': {\n\t\t\tpattern: RegExp('(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"\\'\\\\s]|\\\\s+(?![\\\\s{])|' + string.source + ')*(?=\\\\s*\\\\{)'),\n\t\t\tlookbehind: true\n\t\t},\n\t\t'string': {\n\t\t\tpattern: string,\n\t\t\tgreedy: true\n\t\t},\n\t\t'property': {\n\t\t\tpattern: /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'important': /!important\\b/i,\n\t\t'function': {\n\t\t\tpattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'punctuation': /[(){};:,]/\n\t};\n\n\tPrism.languages.css['atrule'].inside.rest = Prism.languages.css;\n\n\tvar markup = Prism.languages.markup;\n\tif (markup) {\n\t\tmarkup.tag.addInlined('style', 'css');\n\t\tmarkup.tag.addAttribute('style', 'css');\n\t}\n\n}(Prism));\n\n\n/* **********************************************\n     Begin prism-clike.js\n********************************************** */\n\nPrism.languages.clike = {\n\t'comment': [\n\t\t{\n\t\t\tpattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true\n\t\t},\n\t\t{\n\t\t\tpattern: /(^|[^\\\\:])\\/\\/.*/,\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true\n\t\t}\n\t],\n\t'string': {\n\t\tpattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n\t\tgreedy: true\n\t},\n\t'class-name': {\n\t\tpattern: /(\\b(?:class|extends|implements|instanceof|interface|new|trait)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+/i,\n\t\tlookbehind: true,\n\t\tinside: {\n\t\t\t'punctuation': /[.\\\\]/\n\t\t}\n\t},\n\t'keyword': /\\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\\b/,\n\t'boolean': /\\b(?:false|true)\\b/,\n\t'function': /\\b\\w+(?=\\()/,\n\t'number': /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n\t'operator': /[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/,\n\t'punctuation': /[{}[\\];(),.:]/\n};\n\n\n/* **********************************************\n     Begin prism-javascript.js\n********************************************** */\n\nPrism.languages.javascript = Prism.languages.extend('clike', {\n\t'class-name': [\n\t\tPrism.languages.clike['class-name'],\n\t\t{\n\t\t\tpattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$A-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\.(?:constructor|prototype))/,\n\t\t\tlookbehind: true\n\t\t}\n\t],\n\t'keyword': [\n\t\t{\n\t\t\tpattern: /((?:^|\\})\\s*)catch\\b/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t{\n\t\t\tpattern: /(^|[^.]|\\.\\.\\.\\s*)\\b(?:as|assert(?=\\s*\\{)|async(?=\\s*(?:function\\b|\\(|[$\\w\\xA0-\\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\\s*(?:\\{|$))|for|from(?=\\s*(?:['\"]|$))|function|(?:get|set)(?=\\s*(?:[#\\[$\\w\\xA0-\\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\\b/,\n\t\t\tlookbehind: true\n\t\t},\n\t],\n\t// Allow for all non-ASCII characters (See http://stackoverflow.com/a/2008444)\n\t'function': /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*(?:\\.\\s*(?:apply|bind|call)\\s*)?\\()/,\n\t'number': {\n\t\tpattern: RegExp(\n\t\t\t/(^|[^\\w$])/.source +\n\t\t\t'(?:' +\n\t\t\t(\n\t\t\t\t// constant\n\t\t\t\t/NaN|Infinity/.source +\n\t\t\t\t'|' +\n\t\t\t\t// binary integer\n\t\t\t\t/0[bB][01]+(?:_[01]+)*n?/.source +\n\t\t\t\t'|' +\n\t\t\t\t// octal integer\n\t\t\t\t/0[oO][0-7]+(?:_[0-7]+)*n?/.source +\n\t\t\t\t'|' +\n\t\t\t\t// hexadecimal integer\n\t\t\t\t/0[xX][\\dA-Fa-f]+(?:_[\\dA-Fa-f]+)*n?/.source +\n\t\t\t\t'|' +\n\t\t\t\t// decimal bigint\n\t\t\t\t/\\d+(?:_\\d+)*n/.source +\n\t\t\t\t'|' +\n\t\t\t\t// decimal number (integer or float) but no bigint\n\t\t\t\t/(?:\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[Ee][+-]?\\d+(?:_\\d+)*)?/.source\n\t\t\t) +\n\t\t\t')' +\n\t\t\t/(?![\\w$])/.source\n\t\t),\n\t\tlookbehind: true\n\t},\n\t'operator': /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/\n});\n\nPrism.languages.javascript['class-name'][0].pattern = /(\\b(?:class|extends|implements|instanceof|interface|new)\\s+)[\\w.\\\\]+/;\n\nPrism.languages.insertBefore('javascript', 'keyword', {\n\t'regex': {\n\t\tpattern: RegExp(\n\t\t\t// lookbehind\n\t\t\t// eslint-disable-next-line regexp/no-dupe-characters-character-class\n\t\t\t/((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)/.source +\n\t\t\t// Regex pattern:\n\t\t\t// There are 2 regex patterns here. The RegExp set notation proposal added support for nested character\n\t\t\t// classes if the `v` flag is present. Unfortunately, nested CCs are both context-free and incompatible\n\t\t\t// with the only syntax, so we have to define 2 different regex patterns.\n\t\t\t/\\//.source +\n\t\t\t'(?:' +\n\t\t\t/(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}/.source +\n\t\t\t'|' +\n\t\t\t// `v` flag syntax. This supports 3 levels of nested character classes.\n\t\t\t/(?:\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.)*\\])*\\])*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source +\n\t\t\t')' +\n\t\t\t// lookahead\n\t\t\t/(?=(?:\\s|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*(?:$|[\\r\\n,.;:})\\]]|\\/\\/))/.source\n\t\t),\n\t\tlookbehind: true,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'regex-source': {\n\t\t\t\tpattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n\t\t\t\tlookbehind: true,\n\t\t\t\talias: 'language-regex',\n\t\t\t\tinside: Prism.languages.regex\n\t\t\t},\n\t\t\t'regex-delimiter': /^\\/|\\/$/,\n\t\t\t'regex-flags': /^[a-z]+$/,\n\t\t}\n\t},\n\t// This must be declared before keyword because we use \"function\" inside the look-forward\n\t'function-variable': {\n\t\tpattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:async\\s*)?(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n\t\talias: 'function'\n\t},\n\t'parameter': [\n\t\t{\n\t\t\tpattern: /(function(?:\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)?\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\))/,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages.javascript\n\t\t},\n\t\t{\n\t\t\tpattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=>)/i,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages.javascript\n\t\t},\n\t\t{\n\t\t\tpattern: /(\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*=>)/,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages.javascript\n\t\t},\n\t\t{\n\t\t\tpattern: /((?:\\b|\\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\\w\\xA0-\\uFFFF]))(?:(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*)\\(\\s*|\\]\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*\\{)/,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages.javascript\n\t\t}\n\t],\n\t'constant': /\\b[A-Z](?:[A-Z_]|\\dx?)*\\b/\n});\n\nPrism.languages.insertBefore('javascript', 'string', {\n\t'hashbang': {\n\t\tpattern: /^#!.*/,\n\t\tgreedy: true,\n\t\talias: 'comment'\n\t},\n\t'template-string': {\n\t\tpattern: /`(?:\\\\[\\s\\S]|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}|(?!\\$\\{)[^\\\\`])*`/,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'template-punctuation': {\n\t\t\t\tpattern: /^`|`$/,\n\t\t\t\talias: 'string'\n\t\t\t},\n\t\t\t'interpolation': {\n\t\t\t\tpattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'interpolation-punctuation': {\n\t\t\t\t\t\tpattern: /^\\$\\{|\\}$/,\n\t\t\t\t\t\talias: 'punctuation'\n\t\t\t\t\t},\n\t\t\t\t\trest: Prism.languages.javascript\n\t\t\t\t}\n\t\t\t},\n\t\t\t'string': /[\\s\\S]+/\n\t\t}\n\t},\n\t'string-property': {\n\t\tpattern: /((?:^|[,{])[ \\t]*)([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2(?=\\s*:)/m,\n\t\tlookbehind: true,\n\t\tgreedy: true,\n\t\talias: 'property'\n\t}\n});\n\nPrism.languages.insertBefore('javascript', 'operator', {\n\t'literal-property': {\n\t\tpattern: /((?:^|[,{])[ \\t]*)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*:)/m,\n\t\tlookbehind: true,\n\t\talias: 'property'\n\t},\n});\n\nif (Prism.languages.markup) {\n\tPrism.languages.markup.tag.addInlined('script', 'javascript');\n\n\t// add attribute support for all DOM events.\n\t// https://developer.mozilla.org/en-US/docs/Web/Events#Standard_events\n\tPrism.languages.markup.tag.addAttribute(\n\t\t/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,\n\t\t'javascript'\n\t);\n}\n\nPrism.languages.js = Prism.languages.javascript;\n\n\n/* **********************************************\n     Begin prism-file-highlight.js\n********************************************** */\n\n(function () {\n\n\tif (typeof Prism === 'undefined' || typeof document === 'undefined') {\n\t\treturn;\n\t}\n\n\t// https://developer.mozilla.org/en-US/docs/Web/API/Element/matches#Polyfill\n\tif (!Element.prototype.matches) {\n\t\tElement.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;\n\t}\n\n\tvar LOADING_MESSAGE = 'Loading…';\n\tvar FAILURE_MESSAGE = function (status, message) {\n\t\treturn '✖ Error ' + status + ' while fetching file: ' + message;\n\t};\n\tvar FAILURE_EMPTY_MESSAGE = '✖ Error: File does not exist or is empty';\n\n\tvar EXTENSIONS = {\n\t\t'js': 'javascript',\n\t\t'py': 'python',\n\t\t'rb': 'ruby',\n\t\t'ps1': 'powershell',\n\t\t'psm1': 'powershell',\n\t\t'sh': 'bash',\n\t\t'bat': 'batch',\n\t\t'h': 'c',\n\t\t'tex': 'latex'\n\t};\n\n\tvar STATUS_ATTR = 'data-src-status';\n\tvar STATUS_LOADING = 'loading';\n\tvar STATUS_LOADED = 'loaded';\n\tvar STATUS_FAILED = 'failed';\n\n\tvar SELECTOR = 'pre[data-src]:not([' + STATUS_ATTR + '=\"' + STATUS_LOADED + '\"])'\n\t\t+ ':not([' + STATUS_ATTR + '=\"' + STATUS_LOADING + '\"])';\n\n\t/**\n\t * Loads the given file.\n\t *\n\t * @param {string} src The URL or path of the source file to load.\n\t * @param {(result: string) => void} success\n\t * @param {(reason: string) => void} error\n\t */\n\tfunction loadFile(src, success, error) {\n\t\tvar xhr = new XMLHttpRequest();\n\t\txhr.open('GET', src, true);\n\t\txhr.onreadystatechange = function () {\n\t\t\tif (xhr.readyState == 4) {\n\t\t\t\tif (xhr.status < 400 && xhr.responseText) {\n\t\t\t\t\tsuccess(xhr.responseText);\n\t\t\t\t} else {\n\t\t\t\t\tif (xhr.status >= 400) {\n\t\t\t\t\t\terror(FAILURE_MESSAGE(xhr.status, xhr.statusText));\n\t\t\t\t\t} else {\n\t\t\t\t\t\terror(FAILURE_EMPTY_MESSAGE);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t\txhr.send(null);\n\t}\n\n\t/**\n\t * Parses the given range.\n\t *\n\t * This returns a range with inclusive ends.\n\t *\n\t * @param {string | null | undefined} range\n\t * @returns {[number, number | undefined] | undefined}\n\t */\n\tfunction parseRange(range) {\n\t\tvar m = /^\\s*(\\d+)\\s*(?:(,)\\s*(?:(\\d+)\\s*)?)?$/.exec(range || '');\n\t\tif (m) {\n\t\t\tvar start = Number(m[1]);\n\t\t\tvar comma = m[2];\n\t\t\tvar end = m[3];\n\n\t\t\tif (!comma) {\n\t\t\t\treturn [start, start];\n\t\t\t}\n\t\t\tif (!end) {\n\t\t\t\treturn [start, undefined];\n\t\t\t}\n\t\t\treturn [start, Number(end)];\n\t\t}\n\t\treturn undefined;\n\t}\n\n\tPrism.hooks.add('before-highlightall', function (env) {\n\t\tenv.selector += ', ' + SELECTOR;\n\t});\n\n\tPrism.hooks.add('before-sanity-check', function (env) {\n\t\tvar pre = /** @type {HTMLPreElement} */ (env.element);\n\t\tif (pre.matches(SELECTOR)) {\n\t\t\tenv.code = ''; // fast-path the whole thing and go to complete\n\n\t\t\tpre.setAttribute(STATUS_ATTR, STATUS_LOADING); // mark as loading\n\n\t\t\t// add code element with loading message\n\t\t\tvar code = pre.appendChild(document.createElement('CODE'));\n\t\t\tcode.textContent = LOADING_MESSAGE;\n\n\t\t\tvar src = pre.getAttribute('data-src');\n\n\t\t\tvar language = env.language;\n\t\t\tif (language === 'none') {\n\t\t\t\t// the language might be 'none' because there is no language set;\n\t\t\t\t// in this case, we want to use the extension as the language\n\t\t\t\tvar extension = (/\\.(\\w+)$/.exec(src) || [, 'none'])[1];\n\t\t\t\tlanguage = EXTENSIONS[extension] || extension;\n\t\t\t}\n\n\t\t\t// set language classes\n\t\t\tPrism.util.setLanguage(code, language);\n\t\t\tPrism.util.setLanguage(pre, language);\n\n\t\t\t// preload the language\n\t\t\tvar autoloader = Prism.plugins.autoloader;\n\t\t\tif (autoloader) {\n\t\t\t\tautoloader.loadLanguages(language);\n\t\t\t}\n\n\t\t\t// load file\n\t\t\tloadFile(\n\t\t\t\tsrc,\n\t\t\t\tfunction (text) {\n\t\t\t\t\t// mark as loaded\n\t\t\t\t\tpre.setAttribute(STATUS_ATTR, STATUS_LOADED);\n\n\t\t\t\t\t// handle data-range\n\t\t\t\t\tvar range = parseRange(pre.getAttribute('data-range'));\n\t\t\t\t\tif (range) {\n\t\t\t\t\t\tvar lines = text.split(/\\r\\n?|\\n/g);\n\n\t\t\t\t\t\t// the range is one-based and inclusive on both ends\n\t\t\t\t\t\tvar start = range[0];\n\t\t\t\t\t\tvar end = range[1] == null ? lines.length : range[1];\n\n\t\t\t\t\t\tif (start < 0) { start += lines.length; }\n\t\t\t\t\t\tstart = Math.max(0, Math.min(start - 1, lines.length));\n\t\t\t\t\t\tif (end < 0) { end += lines.length; }\n\t\t\t\t\t\tend = Math.max(0, Math.min(end, lines.length));\n\n\t\t\t\t\t\ttext = lines.slice(start, end).join('\\n');\n\n\t\t\t\t\t\t// add data-start for line numbers\n\t\t\t\t\t\tif (!pre.hasAttribute('data-start')) {\n\t\t\t\t\t\t\tpre.setAttribute('data-start', String(start + 1));\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// highlight code\n\t\t\t\t\tcode.textContent = text;\n\t\t\t\t\tPrism.highlightElement(code);\n\t\t\t\t},\n\t\t\t\tfunction (error) {\n\t\t\t\t\t// mark as failed\n\t\t\t\t\tpre.setAttribute(STATUS_ATTR, STATUS_FAILED);\n\n\t\t\t\t\tcode.textContent = error;\n\t\t\t\t}\n\t\t\t);\n\t\t}\n\t});\n\n\tPrism.plugins.fileHighlight = {\n\t\t/**\n\t\t * Executes the File Highlight plugin for all matching `pre` elements under the given container.\n\t\t *\n\t\t * Note: Elements which are already loaded or currently loading will not be touched by this method.\n\t\t *\n\t\t * @param {ParentNode} [container=document]\n\t\t */\n\t\thighlight: function highlight(container) {\n\t\t\tvar elements = (container || document).querySelectorAll(SELECTOR);\n\n\t\t\tfor (var i = 0, element; (element = elements[i++]);) {\n\t\t\t\tPrism.highlightElement(element);\n\t\t\t}\n\t\t}\n\t};\n\n\tvar logged = false;\n\t/** @deprecated Use `Prism.plugins.fileHighlight.highlight` instead. */\n\tPrism.fileHighlight = function () {\n\t\tif (!logged) {\n\t\t\tconsole.warn('Prism.fileHighlight is deprecated. Use `Prism.plugins.fileHighlight.highlight` instead.');\n\t\t\tlogged = true;\n\t\t}\n\t\tPrism.plugins.fileHighlight.highlight.apply(this, arguments);\n\t};\n\n}());\n", "// https://www.json.org/json-en.html\nPrism.languages.json = {\n\t'property': {\n\t\tpattern: /(^|[^\\\\])\"(?:\\\\.|[^\\\\\"\\r\\n])*\"(?=\\s*:)/,\n\t\tlookbehind: true,\n\t\tgreedy: true\n\t},\n\t'string': {\n\t\tpattern: /(^|[^\\\\])\"(?:\\\\.|[^\\\\\"\\r\\n])*\"(?!\\s*:)/,\n\t\tlookbehind: true,\n\t\tgreedy: true\n\t},\n\t'comment': {\n\t\tpattern: /\\/\\/.*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n\t\tgreedy: true\n\t},\n\t'number': /-?\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i,\n\t'punctuation': /[{}[\\],]/,\n\t'operator': /:/,\n\t'boolean': /\\b(?:false|true)\\b/,\n\t'null': {\n\t\tpattern: /\\bnull\\b/,\n\t\talias: 'keyword'\n\t}\n};\n\nPrism.languages.webmanifest = Prism.languages.json;\n", "import * as Prism from 'prismjs';\nimport 'prismjs/components/prism-json';\nimport { isValidJson } from '../utils/isValidJson';\n\nexport const transform = (body: any, language: 'json' | 'html' | 'xml' | 'blob' | 'plaintext' = 'json') => {\n  const content = language === 'json' ? JSON.stringify(body, null, 2) : body\n  if (body) {\n    const formatted = Prism.highlight(content, Prism.languages[language], language)\n\n    let code = formatted.split('\\n')\n      .map((line, num) => `<span class=\"line-number text-slate-700 select-none contents align-top\">${(num + 1).toString().padStart(4, ' ')}  </span>${line}`)\n      .join('\\n');\n\n\n    // add folding to every json object and array\n    if (isValidJson(content)) {\n      code = code\n        .replaceAll('<span class=\"token punctuation\">{</span>', '<details class=\"contents\" open><summary class=\"inline-block brace\"><span class=\"token punctuation\">{</span></summary>')\n        .replaceAll('<span class=\"token punctuation\">[</span>', '<details class=\"contents\" open><summary class=\"inline-block bracket\"><span class=\"token punctuation\">[</span></summary>')\n        .replaceAll('<span class=\"token punctuation\">}</span>', '</details><span class=\"token punctuation inline-block\">}</span>')\n        .replaceAll('<span class=\"token punctuation\">]</span>', '</details><span class=\"token punctuation inline-block\">]</span>')\n    }\n\n    return `<code class=\"language-${language}\">${code}</code>`\n  }\n\n  return ''\n\n}", "export const isValidXml = (str: string) => {\n  try {\n    const parser = new DOMParser();\n    const doc = parser.parseFromString(str, \"application/xml\");\n    return doc.documentElement.nodeName !== \"parsererror\";\n  } catch (_) {\n    return false;\n  }\n}\n", "export function isValidHtml(str: string) {\n  try {\n    const parser = new DOMParser();\n    const doc = parser.parseFromString(str, \"text/html\");\n    return doc.documentElement.nodeName === \"html\";\n  } catch (_) {\n    return false;\n  }\n}\n", "export function isValidBlob(str: any) {\n  return str instanceof Blob;\n}\n", "import { isValid<PERSON>son } from \"@utils/isValidJson\";\nimport { isValidXml } from \"@utils/isValidXml\";\nimport { isValidHtml } from \"@utils/isValidHtml\";\nimport { isValidBlob } from \"@utils/isValidBlob\";\n\nexport function getFormat(str: string) {\n  if (isValidJson(str)) {\n    return \"json\";\n  } else if (isValidXml(str)) {\n    return \"xml\";\n  } else if (isValidHtml(str)) {\n    return \"html\";\n  } else if (isValidBlob(str)) {\n    return \"blob\";\n  } else {\n    return \"plaintext\";\n  }\n}\n", "import { removeStyles } from '@modules/removeStyles'\nimport setCookie from 'set-cookie-parser';\nimport { convertSize } from '@utils/convertSize';\nimport { calculateSize } from '@utils/calculateSize';\nimport { ApiRequestOptions, ApiResponseBody, RequestProps } from '../types';\nimport { transform } from \"@modules/transform\";\nimport { getState } from '@utils/getState';\nimport { App } from 'vue';\nimport { getFormat } from '@utils/getFormat';\n\nexport const handleResponse = (res: ApiResponseBody, options: ApiRequestOptions, props: RequestProps[], index: number, app: App<Element>) => {\n\n  const { doc, testId } = getState()\n\n  // log the request\n  const log = Cypress.log({\n    name: options.method || 'GET',\n    autoEnd: false,\n    message: `${options.url}`\n  }).snapshot('request')\n\n  const { body, status, headers, statusText, duration } = res\n\n  const messageFormatted = `${status}\\u00A0(${statusText})`\n  props[index].status = messageFormatted || ''\n  props[index].time = duration\n  const contentTypeHeader = headers['content-type'] as string\n  const contentLengthHeader = headers['content-length'] as string\n  const contentCookieHeader = headers['set-cookie'] as string\n  const type = typeof body\n  const bodyRaw = type === 'object' ? JSON.stringify(body, null, 2) : body\n\n  if (contentTypeHeader) {\n    const contentType = contentTypeHeader.split(';')[0]\n    const formats = {\n      'text/xml': 'xml',\n      'application/json': 'json',\n      'text/html': 'html',\n      'text/plain': 'plaintext',\n    } as const\n    const definedFormat = formats[contentType as keyof typeof formats]\n    // if format is in the \"formats\" object use that, else try to determine by the function\n    const language = definedFormat || getFormat(body)\n    // format response\n    props[index].responseBody.formatted = transform(body, language)\n    props[index].responseBody.body = bodyRaw\n\n  }\n\n  // format cookies\n  const parsedCookie = setCookie.parse(contentCookieHeader, {\n    decodeValues: true\n  })\n\n  props[index].cookies.body = parsedCookie\n\n  // show \"no content\" message if there’s no response\n  if (!props[index].requestBody.formatted.length) {\n    props[index].requestBody.formatted = '<div class=\"pl-4 text-cy-gray text-xs font-mono\">(No content)</div>'\n  }\n\n  // show \"no content\" message if there’s no response\n  if (!props[index].responseBody.formatted.length) {\n    props[index].responseBody.formatted = '<div class=\"pl-4 text-cy-gray text-xs font-mono\">(No content)</div>'\n  }\n\n  // format response header\n  props[index].responseHeaders.body = headers\n  props[index].responseHeaders.formatted = transform(headers)\n\n  // count content size from header if available, or calculate manually\n  const size = contentLengthHeader ? parseInt(contentLengthHeader) : calculateSize(props[index].responseBody.body?.value)\n  props[index].size = convertSize(size) // convert to readable format (kB, MB...)\n  res.size = size\n\n  const yielded = res\n\n  const findSnapshotElement = () => {\n    return Cypress.$(`#${props[index].id}`, { log: false })\n  }\n\n  // we need to make sure we do the snapshot at a right moment\n  cy.window({ log: false })\n    .then(findSnapshotElement)\n    .then(($el) => {\n\n      // add response to console output\n      log.set({\n        consoleProps() {\n          return {\n            yielded\n          }\n        }\n      })\n\n      // save all props to current window to be loaded\n      window.props[testId] = props\n\n      log.set({ $el });\n      log.snapshot('response').end()\n\n      // scroll to the bottom\n      doc.getElementById('api-view-bottom')?.scrollIntoView()\n\n      // if in snapshot mode, unmount plugin from view\n      if (Cypress.env('snapshotOnly')) {\n        app.unmount()\n        removeStyles()\n      }\n\n      return res\n\n    })\n}", "<template>\n  <div>\n    <div class=\"flex text-cy-gray rounded-sm bg-cy-blue-darkest py-1.5 border-slate-800 border mb-2\">\n      <p\n        data-cy=\"method\"\n        class=\"mx-2 rounded-sm font-mono\"\n        :class=\"methodColor(method)\"\n      >\n        {{ method }}\n      </p>\n      <input\n        data-cy=\"url\"\n        class=\"px-1 inline-block font-mono bg-cy-blue-darkest w-full outline-0\"\n        :value=\"url\"\n        readonly\n      >\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\n  defineProps({\n    method: {\n      default: '',\n      type: String\n    },\n    url: {\n      default: '',\n      type: String\n    }\n  })\n\n  const methodColor = (method: string) => {\n    const methods = {\n      'DELETE': 'text-cy-red',\n      'POST': 'text-cy-green',\n      'PUT': 'text-cy-green',\n      'GET': 'text-cy-blue',\n      'PATCH': 'text-cy-orange',\n      'HEAD': 'text-cy-yellow'\n    }\n    return methods[method as keyof typeof methods]\n  }\n\n</script>", "<template>\n  <div>\n    <div class=\"flex text-cy-gray  py-1.5 mb-2\">\n      <p>\n        Status:<span\n          data-cy=\"status\"\n          class=\"mx-2 font-mono\"\n          :class=\"statusColor(status)\"\n        >{{ status }}</span>\n      </p>\n      <p>\n        Duration:<span\n          data-cy=\"time\"\n          class=\"mx-2 font-mono text-cy-green\"\n        >{{ time }}&nbsp;ms</span>\n      </p>\n      <p v-if=\"size\">\n        Size:<span\n          data-cy=\"size\"\n          class=\"mx-2 font-mono text-cy-green\"\n        >{{ size }}</span>\n      </p>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\n  defineProps({\n    status: {\n      default: '',\n      type: String\n    },\n    time: {\n      default: 0,\n      type: Number\n    },\n    size: {\n      default: '',\n      type: String\n    }\n  })\n\n  const statusColor = (status: string) => {\n    const statusCategory = status.substring(0,1)\n    const statuses = {\n      '2': 'text-cy-green',\n      '3': 'text-cy-orange',\n      '4': 'text-cy-red',\n      '5': 'text-cy-red'\n    }\n    return statuses[statusCategory as keyof typeof statuses]\n  }\n\n</script>", "import base from \"../style.css\";\nimport timeline from \"../timeline.css\";\nimport { getState } from \"../utils/getState\";\n\nexport const addStyles = () => {\n\n  const { doc } = getState()\n  // append styles\n  const head = doc.head || doc.getElementsByTagName('head')[0]\n\n  // add plugin styles\n  const style = doc.createElement('style');\n  style.setAttribute('id', 'api-plugin-styles')\n  head.appendChild(style);\n  style.appendChild(doc.createTextNode(base));\n\n  // add timeline styles\n  const reporterEl = top?.document.querySelector('#unified-reporter') || top?.document.querySelector('#app')\n  const reporterStyleEl = document.createElement('style')\n  reporterEl?.appendChild(reporterStyleEl)\n  reporterStyleEl.appendChild(doc.createTextNode(timeline));\n\n}", "import { App } from \"vue\";\nimport { addStyles } from \"./addStyles\";\nimport { getState } from \"../utils/getState\";\n\nexport const mountPlugin = (app: App<Element>) => {\n\n  const { doc } = getState()\n  addStyles()\n\n  // create an element where our plugin will mount\n  const root = doc.createElement('div');\n  root.setAttribute('id', 'api-plugin-root')\n  doc.body.appendChild(root);\n\n  const plugin = doc.getElementById('api-plugin-root')\n  app.mount(plugin as Element)\n\n}", "import { getState } from '@utils/getState'\nimport { RequestProps } from \"../types\"\nimport { reactive, createApp } from \"vue\"\nimport App from \"../components/App.vue\";\nimport { mountPlugin } from \"./mountPlugin\";\nconst { _ } = Cypress\n\nexport const initialize = () => {\n\n  const propItem: RequestProps = {\n    id: _.uniqueId(),\n    method: 'GET',\n    status: '',\n    time: 0,\n    size: '',\n    url: '',\n    auth: {\n      body: {},\n      formatted: ''\n    },\n    query: {\n      body: {},\n      formatted: ''\n    },\n    requestHeaders: {\n      body: {},\n      formatted: ''\n    },\n    requestBody: {\n      body: {},\n      formatted: ''\n    },\n    responseBody: {\n      body: {},\n      formatted: ''\n    },\n    responseHeaders: {\n      body: {},\n      formatted: ''\n    },\n    cookies: {\n      body: {}\n    }\n  }\n\n  const { doc, attempt, testId } = getState()\n\n  // get the number of retry, 0 if first attempt\n  const isRetry = attempt !== 0\n  const hasNavigated = doc.URL !== 'about:blank'\n\n  // determine if there are props from the same test but previous cy.api() call\n  const propsExist = window.props[testId]?.length ? true : false\n\n  // initialize an empty array for current test if this is a first call of cy.api() in current test\n  const currentProps: RequestProps[] = propsExist && !isRetry ? window.props[testId] : [] as RequestProps[]\n\n  // add empty props object to be filled in upcoming call\n  currentProps.push(propItem)\n\n  // load props saved into window if any present in current test\n  const props = reactive(currentProps)\n\n  const app = createApp(App, {\n    props\n  })\n\n\n  // mount plugin only on first call in the test, on retry, or when we left the initial page with cy.visit()\n  if (!propsExist || isRetry || Cypress.env('snapshotOnly') || hasNavigated) {\n    mountPlugin(app)\n  }\n\n  return { app, props }\n\n}", "import { RequestProps } from \"../types\"\nimport { transform } from \"./transform\"\n\nexport const transformData = (props: RequestProps[], index: number) => {\n\n  // format request body\n  props[index].requestBody.formatted = transform(props[index].requestBody.body)\n  // format request headers\n  props[index].requestHeaders.formatted = transform(props[index].requestHeaders.body)\n  // format query\n  props[index].query.formatted = transform(props[index].query.body)\n  // format auth\n  props[index].auth.formatted = transform(props[index].auth.body)\n\n}", "export const isValidUrl = (str: string | URL) => {\n  try {\n    return new URL(str);\n  } catch (e) {\n    return null;\n  }\n}\n", "export const isValidIp = (str: string) => {\n  return /(\\b(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b)/g\n    .test(str);\n}", "import { isValidUrl } from \"@utils/isValidUrl\";\nimport { isValidIp } from \"@utils/isValidIp\";\n\nexport const isValidUrlOrIp = (str: string) => {\n  return isValidUrl(str) || isValidIp(str)\n}\n", "import { isValidUrlOrIp } from \"@utils/isValidUrlOrIp\"\nimport { ApiRequestOptions, RequestProps } from \"../types\"\nconst { _ } = Cypress\n\nexport const cloneProps = (props: RequestProps[], index: number, options: ApiRequestOptions) => {\n  props[index].method = _.cloneDeep(options.method) || 'GET'\n  props[index].url = isValidUrlOrIp(options.url) ? options.url : Cypress.config('baseUrl') + options.url\n  props[index].query.body = _.cloneDeep(options.qs)\n  props[index].auth.body = _.cloneDeep(options.auth)\n  props[index].requestHeaders.body = _.cloneDeep(options.headers)\n  props[index].requestBody.body = _.cloneDeep(options.body)\n}", "import { resolveOptions } from '@utils/resolveOptions';\nimport { ApiRequestOptions } from '../types';\nimport { anonymize } from '@utils/anonymize';\nimport { handleResponse } from '@modules/handleResponse';\nimport { initialize } from './initialize';\nimport { transformData } from './transformData';\nimport { cloneProps } from './cloneProps';\n\n// make a copy of cy.request() function\n// this prevents unusual behavior when using requestMode\nconst requestFn = cy.request.bind({})\n\nexport const api = (...params: Partial<ApiRequestOptions>[]) => {\n\n  const { props, app } = initialize()\n  const options: ApiRequestOptions = resolveOptions(...params)\n  const index = props.length - 1\n  cloneProps(props, index, options)\n  // hide credentials if the options was set up\n  if (Cypress.env('hideCredentials')) props[index] = anonymize(props[index])\n  transformData(props, index)\n\n  return requestFn({ ...options, log: false }).then(res => handleResponse(res, options, props, index, app))\n}", "/// <reference types=\"cypress\" />\n\nimport './types'\nimport { api } from './modules/api'\n\nbefore(() => {\n  // initialize global props object\n  window.props = {}\n})\n\nCypress.Commands.addAll({ api })\n\nCypress.Commands.overwrite('request', (originalFn, ...args) => {\n  if (Cypress.env('requestMode')) {\n    return api(...args)\n  } else {\n    return originalFn(...args)\n  }\n})\n\nexport { api }"], "names": ["_", "methods", "validate<PERSON><PERSON><PERSON>", "str", "resolveOptions", "args", "o", "userOptions", "anonymize", "options", "optionsUndefined", "anonymizeOptions", "_a", "_b", "_c", "_d", "k", "_e", "_f", "_g", "getState", "doc", "attempt", "testId", "removeStyles", "style", "defaultParseOptions", "isNonEmptyString", "parseString", "setCookieValue", "parts", "nameValuePairStr", "parsed", "parseNameValuePair", "name", "value", "e", "cookie", "part", "sides", "key", "nameValueArr", "parse", "input", "sch", "cookies", "splitCookiesString", "cookiesString", "cookiesStrings", "pos", "start", "ch", "lastComma", "nextStart", "cookiesSeparatorFound", "skipWhitespace", "notSpecialChar", "setCookieModule", "<PERSON><PERSON><PERSON><PERSON>", "convertSize", "size", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "calculateSize", "cleanString", "finalString", "_self", "Prism", "lang", "uniqueId", "plainTextGrammar", "encode", "tokens", "Token", "obj", "deepClone", "visited", "clone", "id", "v", "element", "m", "language", "err", "src", "scripts", "className", "defaultActivation", "no", "classList", "redef", "inside", "before", "insert", "root", "grammar", "ret", "token", "newToken", "old", "DFS", "callback", "type", "objId", "property", "propertyType", "async", "container", "env", "parent", "code", "insertHighlightedCode", "highlightedCode", "worker", "evt", "text", "rest", "tokenList", "LinkedList", "addAfter", "matchGrammar", "toArray", "hooks", "callbacks", "content", "alias", "matchedStr", "stringify", "s", "aliases", "attributes", "matchPattern", "pattern", "lookbehind", "match", "lookbehindLength", "startNode", "startPos", "rematch", "patterns", "j", "patternObj", "greedy", "flags", "currentNode", "removeCount", "from", "to", "p", "matchStr", "after", "reach", "removeFrom", "<PERSON><PERSON><PERSON><PERSON>", "wrapped", "nested<PERSON><PERSON><PERSON>", "head", "tail", "list", "node", "next", "newNode", "count", "array", "message", "immediateClose", "script", "highlightAutomatically<PERSON>allback", "readyState", "module", "global", "tagName", "includedCdataInside", "def", "attrName", "string", "markup", "LOADING_MESSAGE", "FAILURE_MESSAGE", "status", "FAILURE_EMPTY_MESSAGE", "EXTENSIONS", "STATUS_ATTR", "STATUS_LOADING", "STATUS_LOADED", "STATUS_FAILED", "SELECTOR", "loadFile", "success", "error", "xhr", "parseRange", "range", "comma", "end", "pre", "extension", "autoloader", "lines", "elements", "logged", "transform", "body", "Prism.highlight", "Prism.languages", "line", "num", "isValidXml", "isValidHtml", "isValidBlob", "getFormat", "handleResponse", "res", "props", "index", "app", "log", "headers", "statusText", "duration", "messageFormatted", "contentTypeHeader", "contentLengthHeader", "contentCookieHeader", "bodyRaw", "contentType", "parsed<PERSON><PERSON><PERSON>", "yielded", "findSnapshotElement", "$el", "methodColor", "method", "statusColor", "statusCategory", "addStyles", "base", "reporter<PERSON><PERSON>", "reporterStyleEl", "timeline", "mountPlugin", "plugin", "initialize", "propItem", "isRetry", "hasNavigated", "propsExist", "currentProps", "reactive", "createApp", "App", "transformData", "isValidUrl", "isValidIp", "isValidUrlOrIp", "cloneProps", "requestFn", "api", "params", "originalFn"], "mappings": "oCAAM,CAAEA,EAAAA,EAAM,EAAA,QAEDC,GAAU,CACrB,MACA,OACA,MACA,OACA,SACA,UACA,QACA,OACA,OACA,QACA,OACA,QACA,WACA,YACA,SACA,SACA,aACA,WACA,QACA,WACA,SACA,YACA,cACA,QACA,SACA,WAGWC,GAAkBC,GACtBH,GAAE,SAASG,CAAG,GAAKH,GAAE,SAASC,GAASE,EAAI,YAAA,CAAa,EC/B3D,CAAEH,EAAAA,CAAM,EAAA,QAEDI,GAAiB,IAAIC,IAAe,CAE3CL,EAAE,WAAWK,EAAK,EAAE,GACtBA,EAAK,MAAK,EAGZ,MAAMC,EAAS,CAAA,EACTC,EAAcD,EAEpB,OAAIN,EAAE,SAASK,EAAK,EAAE,EAClBL,EAAA,OAAOO,EAAaF,EAAK,EAAE,EACpBA,EAAK,SAAW,EACzBC,EAAE,IAAMD,EAAK,GACJA,EAAK,SAAW,EACrBH,GAAeG,EAAK,EAAE,GACxBC,EAAE,OAASD,EAAK,GAChBC,EAAE,IAAMD,EAAK,KAEbC,EAAE,IAAMD,EAAK,GACbC,EAAE,KAAOD,EAAK,IAEPA,EAAK,SAAW,IACzBC,EAAE,OAASD,EAAK,GAChBC,EAAE,IAAMD,EAAK,GACbC,EAAE,KAAOD,EAAK,IAGTE,CAET,EC9BaC,GAAaC,GAAyB,mBAEjD,MAAMC,EAAmB,QAAQ,IAAI,wBAAwB,IAAM,OAE7DC,EAA2C,CAC/C,KAAM,CAAE,EACR,KAAM,CAAE,EACR,QAAS,CAAE,EACX,GAAI,CAAE,EACN,GAAG,QAAQ,IAAI,wBAAwB,CAAA,EAGzC,OAAID,KAEFE,EAAAD,EAAiB,OAAjB,MAAAC,EAAuB,KAAK,OAAQ,WAAY,OAAQ,WAAY,WACpEC,EAAAF,EAAiB,UAAjB,MAAAE,EAA0B,KAAK,gBAAiB,gBAAiB,WAAY,aAC5DC,EAAAH,EAAA,OAAA,MAAAG,EAAM,KAAK,OAAQ,cAGrBC,EAAAJ,EAAA,OAAA,MAAAI,EAAM,QAAYC,GAAA,CAC7BP,EAAQ,KAAK,MAAQA,EAAQ,KAAK,KAAKO,KACjCP,EAAA,KAAK,KAAKO,GAAKP,GAAA,YAAAA,EAAS,KAAK,KAAKO,GAAG,QAAQ,KAAM,KAC5D,IAGcC,EAAAN,EAAA,UAAA,MAAAM,EAAS,QAAYD,GAAA,CAChCP,EAAQ,eAAe,MAAQA,EAAQ,eAAe,KAAKO,KACrDP,EAAA,eAAe,KAAKO,GAAKP,GAAA,YAAAA,EAAS,eAAe,KAAKO,GAAG,QAAQ,KAAM,KAChF,IAGcE,EAAAP,EAAA,OAAA,MAAAO,EAAM,QAAYF,GAAA,CAC7BP,EAAQ,YAAY,MAAQA,EAAQ,YAAY,KAAKO,KAE/CP,EAAA,YAAY,KAAKO,GAAKP,GAAA,YAAAA,EAAS,YAAY,KAAKO,GAAG,QAAQ,KAAM,KAC1E,IAGcG,EAAAR,EAAA,KAAA,MAAAQ,EAAI,QAAYH,GAAA,CAC3BP,EAAQ,MAAM,MAAQA,EAAQ,MAAM,KAAKO,KAEnCP,EAAA,MAAM,KAAKO,GAAKP,GAAA,YAAAA,EAAS,MAAM,KAAKO,GAAG,QAAQ,KAAM,KAC9D,GAGIP,CACT,EChDaW,EAAW,IAAK,CAErB,MAAAC,EAAgB,GAAG,MAAM,UAAU,EAEnCC,EAAkB,GAAG,MAAM,UAAU,EAAE,cAEvCC,EAAiB,GAAG,MAAM,MAAM,EAAE,GACjC,MAAA,CAAE,IAAAF,EAAK,QAAAC,EAAS,OAAAC,EACzB,ECNaC,GAAe,IAAK,CAEzB,KAAA,CAAE,IAAAH,GAAQD,IAEVK,EAAQJ,EAAI,eAAe,mBAAmB,EACpDI,GAAA,MAAAA,EAAO,QAET,mICPIC,EAAsB,CACxB,aAAc,GACd,IAAK,GACL,OAAQ,EACV,EAEA,SAASC,EAAiBxB,EAAK,CAC7B,OAAO,OAAOA,GAAQ,UAAY,CAAC,CAACA,EAAI,KAAI,CAC9C,CAEA,SAASyB,EAAYC,EAAgBpB,EAAS,CAC5C,IAAIqB,EAAQD,EAAe,MAAM,GAAG,EAAE,OAAOF,CAAgB,EAEzDI,EAAmBD,EAAM,QACzBE,EAASC,GAAmBF,CAAgB,EAC5CG,EAAOF,EAAO,KACdG,EAAQH,EAAO,MAEnBvB,EAAUA,EACN,OAAO,OAAO,GAAIiB,EAAqBjB,CAAO,EAC9CiB,EAEJ,GAAI,CACFS,EAAQ1B,EAAQ,aAAe,mBAAmB0B,CAAK,EAAIA,CAC5D,OAAQC,EAAP,CACA,QAAQ,MACN,8EACED,EACA,gEACFC,CACN,CACG,CAED,IAAIC,EAAS,CACX,KAAMH,EACN,MAAOC,CACX,EAEE,OAAAL,EAAM,QAAQ,SAAUQ,EAAM,CAC5B,IAAIC,EAAQD,EAAK,MAAM,GAAG,EACtBE,EAAMD,EAAM,MAAO,EAAC,SAAQ,EAAG,cAC/BJ,EAAQI,EAAM,KAAK,GAAG,EACtBC,IAAQ,UACVH,EAAO,QAAU,IAAI,KAAKF,CAAK,EACtBK,IAAQ,UACjBH,EAAO,OAAS,SAASF,EAAO,EAAE,EACzBK,IAAQ,SACjBH,EAAO,OAAS,GACPG,IAAQ,WACjBH,EAAO,SAAW,GACTG,IAAQ,WACjBH,EAAO,SAAWF,EAElBE,EAAOG,GAAOL,CAEpB,CAAG,EAEME,CACT,CAEA,SAASJ,GAAmBF,EAAkB,CAG5C,IAAIG,EAAO,GACPC,EAAQ,GACRM,EAAeV,EAAiB,MAAM,GAAG,EAC7C,OAAIU,EAAa,OAAS,GACxBP,EAAOO,EAAa,QACpBN,EAAQM,EAAa,KAAK,GAAG,GAE7BN,EAAQJ,EAGH,CAAE,KAAMG,EAAM,MAAOC,CAAK,CACnC,CAEA,SAASO,GAAMC,EAAOlC,EAAS,CAK7B,GAJAA,EAAUA,EACN,OAAO,OAAO,GAAIiB,EAAqBjB,CAAO,EAC9CiB,EAEA,CAACiB,EACH,OAAKlC,EAAQ,IAGJ,GAFA,GAMX,GAAIkC,EAAM,SAAWA,EAAM,QAAQ,cAEjCA,EAAQA,EAAM,QAAQ,sBACbA,EAAM,QAAS,CAExB,IAAIC,EACFD,EAAM,QACJ,OAAO,KAAKA,EAAM,OAAO,EAAE,KAAK,SAAUH,EAAK,CAC7C,OAAOA,EAAI,YAAa,IAAK,YACvC,CAAS,GAGD,CAACI,GAAOD,EAAM,QAAQ,QAAU,CAAClC,EAAQ,QAC3C,QAAQ,KACN,kOACR,EAEIkC,EAAQC,CACT,CASD,GARK,MAAM,QAAQD,CAAK,IACtBA,EAAQ,CAACA,CAAK,GAGhBlC,EAAUA,EACN,OAAO,OAAO,GAAIiB,EAAqBjB,CAAO,EAC9CiB,EAECjB,EAAQ,IAIN,CACL,IAAIoC,EAAU,CAAA,EACd,OAAOF,EAAM,OAAOhB,CAAgB,EAAE,OAAO,SAAUkB,EAAS1C,EAAK,CACnE,IAAIkC,EAAST,EAAYzB,EAAKM,CAAO,EACrC,OAAAoC,EAAQR,EAAO,MAAQA,EAChBQ,CACR,EAAEA,CAAO,CACX,KAVC,QAAOF,EAAM,OAAOhB,CAAgB,EAAE,IAAI,SAAUxB,EAAK,CACvD,OAAOyB,EAAYzB,EAAKM,CAAO,CACrC,CAAK,CASL,CAaA,SAASqC,GAAmBC,EAAe,CACzC,GAAI,MAAM,QAAQA,CAAa,EAC7B,OAAOA,EAET,GAAI,OAAOA,GAAkB,SAC3B,MAAO,GAGT,IAAIC,EAAiB,CAAA,EACjBC,EAAM,EACNC,EACAC,EACAC,EACAC,EACAC,EAEJ,SAASC,GAAiB,CACxB,KAAON,EAAMF,EAAc,QAAU,KAAK,KAAKA,EAAc,OAAOE,CAAG,CAAC,GACtEA,GAAO,EAET,OAAOA,EAAMF,EAAc,MAC5B,CAED,SAASS,GAAiB,CACxB,OAAAL,EAAKJ,EAAc,OAAOE,CAAG,EAEtBE,IAAO,KAAOA,IAAO,KAAOA,IAAO,GAC3C,CAED,KAAOF,EAAMF,EAAc,QAAQ,CAIjC,IAHAG,EAAQD,EACRK,EAAwB,GAEjBC,EAAc,GAEnB,GADAJ,EAAKJ,EAAc,OAAOE,CAAG,EACzBE,IAAO,IAAK,CAQd,IANAC,EAAYH,EACZA,GAAO,EAEPM,IACAF,EAAYJ,EAELA,EAAMF,EAAc,QAAUS,EAAc,GACjDP,GAAO,EAILA,EAAMF,EAAc,QAAUA,EAAc,OAAOE,CAAG,IAAM,KAE9DK,EAAwB,GAExBL,EAAMI,EACNL,EAAe,KAAKD,EAAc,UAAUG,EAAOE,CAAS,CAAC,EAC7DF,EAAQD,GAIRA,EAAMG,EAAY,CAE5B,MACQH,GAAO,GAIP,CAACK,GAAyBL,GAAOF,EAAc,SACjDC,EAAe,KAAKD,EAAc,UAAUG,EAAOH,EAAc,MAAM,CAAC,CAE3E,CAED,OAAOC,CACT,CAEAS,EAAc,QAAGf,GACGgB,EAAA,QAAA,MAAGhB,GACGgB,EAAA,QAAA,YAAG9B,EAC7B8B,EAAA,QAAA,mBAAoCZ,GC3N9B,SAAUa,GAAYC,EAAY,CACtC,MAAMC,EAAYD,GAAQ,EAAI,EAAI,KAAK,MAAM,KAAK,IAAIA,CAAI,EAAI,KAAK,IAAI,IAAI,CAAC,EAC5E,OAAeA,EAAO,KAAK,IAAI,KAAMC,CAAC,GAAG,QAAQ,CAAC,EAAI,EAAI,OAAW,CAAC,IAAK,KAAM,KAAM,KAAM,IAAI,EAAEA,EACrG,CCHa,MAAAC,EAAenB,GAAuB,CAE7C,GAAA,OAAOA,GAAU,SACZ,MAAA,GACR,GAAU,OAAOA,GAAU,SAEtB,GAAA,CACF,YAAK,MAAMA,CAAK,EACT,SAEA,MAAA,EACR,KAEM,OAAA,EAEX,ECbaoB,GAAiB5B,GAAiB,CAE7C,GAAI,CAACA,EACI,MAAA,GAKT,MAAM6B,EAFc7B,EAAM,WAEM,QAAQ,QAAS;AAAA,CAAI,EAE/C8B,EAAcH,EAAYE,CAAW,EAAIA,EAAY,QAAQ,MAAO,EAAE,EAAIA,EAGzE,OAFW,IAAI,KAAK,CAACC,CAAW,CAAC,EAAE,IAI5C,kCCVA,IAAIC,EAAS,OAAO,OAAW,IAC5B,OAEA,OAAO,kBAAsB,KAAe,gBAAgB,kBAC1D,KACA,CAAE,EAGP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAQA,IAAIC,EAAS,SAAUD,EAAO,CAG7B,IAAIE,EAAO,0CACPC,EAAW,EAGXC,EAAmB,CAAA,EAGnBtE,EAAI,CAsBP,OAAQkE,EAAM,OAASA,EAAM,MAAM,OAsBnC,4BAA6BA,EAAM,OAASA,EAAM,MAAM,4BAWxD,KAAM,CACL,OAAQ,SAASK,EAAOC,EAAQ,CAC/B,OAAIA,aAAkBC,EACd,IAAIA,EAAMD,EAAO,KAAMD,EAAOC,EAAO,OAAO,EAAGA,EAAO,KAAK,EACxD,MAAM,QAAQA,CAAM,EACvBA,EAAO,IAAID,CAAM,EAEjBC,EAAO,QAAQ,KAAM,OAAO,EAAE,QAAQ,KAAM,MAAM,EAAE,QAAQ,UAAW,GAAG,CAElF,EAkBD,KAAM,SAAU,EAAG,CAClB,OAAO,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,EAAG,EAAE,CACpD,EAQD,MAAO,SAAUE,EAAK,CACrB,OAAKA,EAAI,MACR,OAAO,eAAeA,EAAK,OAAQ,CAAE,MAAO,EAAEL,CAAQ,CAAE,EAElDK,EAAI,IACX,EAYD,MAAO,SAASC,EAAUrE,EAAGsE,EAAS,CACrCA,EAAUA,GAAW,GAErB,IAAIC,EAAWC,EACf,OAAQ9E,EAAE,KAAK,KAAKM,CAAC,EAAC,CACrB,IAAK,SAEJ,GADAwE,EAAK9E,EAAE,KAAK,MAAMM,CAAC,EACfsE,EAAQE,GACX,OAAOF,EAAQE,GAEhBD,EAA4C,CAAE,EAC9CD,EAAQE,GAAMD,EAEd,QAASrC,KAAOlC,EACXA,EAAE,eAAekC,CAAG,IACvBqC,EAAMrC,GAAOmC,EAAUrE,EAAEkC,GAAMoC,CAAO,GAIxC,OAA2BC,EAE5B,IAAK,QAEJ,OADAC,EAAK9E,EAAE,KAAK,MAAMM,CAAC,EACfsE,EAAQE,GACJF,EAAQE,IAEhBD,EAAQ,CAAA,EACRD,EAAQE,GAAMD,EAE2BvE,EAAK,QAAQ,SAAUyE,EAAGlB,EAAG,CACrEgB,EAAMhB,GAAKc,EAAUI,EAAGH,CAAO,CACtC,CAAO,EAE0BC,GAE5B,QACC,OAAOvE,CACR,CACD,EAUD,YAAa,SAAU0E,EAAS,CAC/B,KAAOA,GAAS,CACf,IAAIC,EAAIb,EAAK,KAAKY,EAAQ,SAAS,EACnC,GAAIC,EACH,OAAOA,EAAE,GAAG,cAEbD,EAAUA,EAAQ,aAClB,CACD,MAAO,MACP,EASD,YAAa,SAAUA,EAASE,EAAU,CAGzCF,EAAQ,UAAYA,EAAQ,UAAU,QAAQ,OAAOZ,EAAM,IAAI,EAAG,EAAE,EAIpEY,EAAQ,UAAU,IAAI,YAAcE,CAAQ,CAC5C,EASD,cAAe,UAAY,CAC1B,GAAI,OAAO,SAAa,IACvB,OAAO,KAER,GAAI,kBAAmB,UAAY,EAAI,EACtC,OAA2B,SAAS,cAOrC,GAAI,CACH,MAAM,IAAI,KACV,OAAQC,EAAP,CAQD,IAAIC,GAAO,qCAAqC,KAAKD,EAAI,KAAK,GAAK,IAAI,GACvE,GAAIC,EAAK,CACR,IAAIC,EAAU,SAAS,qBAAqB,QAAQ,EACpD,QAASxB,KAAKwB,EACb,GAAIA,EAAQxB,GAAG,KAAOuB,EACrB,OAAOC,EAAQxB,EAGjB,CACD,OAAO,IACP,CACD,EAqBD,SAAU,SAAUmB,EAASM,EAAWC,EAAmB,CAG1D,QAFIC,EAAK,MAAQF,EAEVN,GAAS,CACf,IAAIS,EAAYT,EAAQ,UACxB,GAAIS,EAAU,SAASH,CAAS,EAC/B,MAAO,GAER,GAAIG,EAAU,SAASD,CAAE,EACxB,MAAO,GAERR,EAAUA,EAAQ,aAClB,CACD,MAAO,CAAC,CAACO,CACT,CACD,EASD,UAAW,CAIV,MAAOjB,EACP,UAAWA,EACX,KAAMA,EACN,IAAKA,EA8BL,OAAQ,SAAUQ,EAAIY,EAAO,CAC5B,IAAItB,EAAOpE,EAAE,KAAK,MAAMA,EAAE,UAAU8E,EAAG,EAEvC,QAAStC,KAAOkD,EACftB,EAAK5B,GAAOkD,EAAMlD,GAGnB,OAAO4B,CACP,EA6ED,aAAc,SAAUuB,EAAQC,EAAQC,EAAQC,EAAM,CACrDA,EAAOA,GAA4B9F,EAAE,UACrC,IAAI+F,EAAUD,EAAKH,GAEfK,EAAM,CAAA,EAEV,QAASC,KAASF,EACjB,GAAIA,EAAQ,eAAeE,CAAK,EAAG,CAElC,GAAIA,GAASL,EACZ,QAASM,KAAYL,EAChBA,EAAO,eAAeK,CAAQ,IACjCF,EAAIE,GAAYL,EAAOK,IAMrBL,EAAO,eAAeI,CAAK,IAC/BD,EAAIC,GAASF,EAAQE,GAEtB,CAGF,IAAIE,EAAML,EAAKH,GACf,OAAAG,EAAKH,GAAUK,EAGfhG,EAAE,UAAU,IAAIA,EAAE,UAAW,SAAUwC,EAAKL,EAAO,CAC9CA,IAAUgE,GAAO3D,GAAOmD,IAC3B,KAAKnD,GAAOwD,EAElB,CAAK,EAEMA,CACP,EAGD,IAAK,SAASI,EAAI9F,EAAG+F,EAAUC,EAAM1B,EAAS,CAC7CA,EAAUA,GAAW,GAErB,IAAI2B,EAAQvG,EAAE,KAAK,MAEnB,QAAS6D,KAAKvD,EACb,GAAIA,EAAE,eAAeuD,CAAC,EAAG,CACxBwC,EAAS,KAAK/F,EAAGuD,EAAGvD,EAAEuD,GAAIyC,GAAQzC,CAAC,EAEnC,IAAI2C,EAAWlG,EAAEuD,GACb4C,EAAezG,EAAE,KAAK,KAAKwG,CAAQ,EAEnCC,IAAiB,UAAY,CAAC7B,EAAQ2B,EAAMC,CAAQ,IACvD5B,EAAQ2B,EAAMC,CAAQ,GAAK,GAC3BJ,EAAII,EAAUH,EAAU,KAAMzB,CAAO,GAC3B6B,IAAiB,SAAW,CAAC7B,EAAQ2B,EAAMC,CAAQ,KAC7D5B,EAAQ2B,EAAMC,CAAQ,GAAK,GAC3BJ,EAAII,EAAUH,EAAUxC,EAAGe,CAAO,EAEnC,CAEF,CACD,EAED,QAAS,CAAE,EAcX,aAAc,SAAU8B,EAAOL,EAAU,CACxCrG,EAAE,kBAAkB,SAAU0G,EAAOL,CAAQ,CAC7C,EAiBD,kBAAmB,SAAUM,EAAWD,EAAOL,EAAU,CACxD,IAAIO,EAAM,CACT,SAAUP,EACV,UAAWM,EACX,SAAU,kGACd,EAEG3G,EAAE,MAAM,IAAI,sBAAuB4G,CAAG,EAEtCA,EAAI,SAAW,MAAM,UAAU,MAAM,MAAMA,EAAI,UAAU,iBAAiBA,EAAI,QAAQ,CAAC,EAEvF5G,EAAE,MAAM,IAAI,gCAAiC4G,CAAG,EAEhD,QAAS/C,EAAI,EAAGmB,EAAUA,EAAU4B,EAAI,SAAS/C,MAChD7D,EAAE,iBAAiBgF,EAAS0B,IAAU,GAAME,EAAI,QAAQ,CAEzD,EA8BD,iBAAkB,SAAU5B,EAAS0B,EAAOL,EAAU,CAErD,IAAInB,EAAWlF,EAAE,KAAK,YAAYgF,CAAO,EACrCe,EAAU/F,EAAE,UAAUkF,GAG1BlF,EAAE,KAAK,YAAYgF,EAASE,CAAQ,EAGpC,IAAI2B,EAAS7B,EAAQ,cACjB6B,GAAUA,EAAO,SAAS,YAAW,IAAO,OAC/C7G,EAAE,KAAK,YAAY6G,EAAQ3B,CAAQ,EAGpC,IAAI4B,EAAO9B,EAAQ,YAEf4B,EAAM,CACT,QAAS5B,EACT,SAAUE,EACV,QAASa,EACT,KAAMe,CACV,EAEG,SAASC,EAAsBC,EAAiB,CAC/CJ,EAAI,gBAAkBI,EAEtBhH,EAAE,MAAM,IAAI,gBAAiB4G,CAAG,EAEhCA,EAAI,QAAQ,UAAYA,EAAI,gBAE5B5G,EAAE,MAAM,IAAI,kBAAmB4G,CAAG,EAClC5G,EAAE,MAAM,IAAI,WAAY4G,CAAG,EAC3BP,GAAYA,EAAS,KAAKO,EAAI,OAAO,CACrC,CAUD,GARA5G,EAAE,MAAM,IAAI,sBAAuB4G,CAAG,EAGtCC,EAASD,EAAI,QAAQ,cACjBC,GAAUA,EAAO,SAAS,YAAW,IAAO,OAAS,CAACA,EAAO,aAAa,UAAU,GACvFA,EAAO,aAAa,WAAY,GAAG,EAGhC,CAACD,EAAI,KAAM,CACd5G,EAAE,MAAM,IAAI,WAAY4G,CAAG,EAC3BP,GAAYA,EAAS,KAAKO,EAAI,OAAO,EACrC,MACA,CAID,GAFA5G,EAAE,MAAM,IAAI,mBAAoB4G,CAAG,EAE/B,CAACA,EAAI,QAAS,CACjBG,EAAsB/G,EAAE,KAAK,OAAO4G,EAAI,IAAI,CAAC,EAC7C,MACA,CAED,GAAIF,GAASxC,EAAM,OAAQ,CAC1B,IAAI+C,EAAS,IAAI,OAAOjH,EAAE,QAAQ,EAElCiH,EAAO,UAAY,SAAUC,EAAK,CACjCH,EAAsBG,EAAI,IAAI,CACnC,EAEID,EAAO,YAAY,KAAK,UAAU,CACjC,SAAUL,EAAI,SACd,KAAMA,EAAI,KACV,eAAgB,EAChB,CAAA,CAAC,CACN,MACIG,EAAsB/G,EAAE,UAAU4G,EAAI,KAAMA,EAAI,QAASA,EAAI,QAAQ,CAAC,CAEvE,EAsBD,UAAW,SAAUO,EAAMpB,EAASb,EAAU,CAC7C,IAAI0B,EAAM,CACT,KAAMO,EACN,QAASpB,EACT,SAAUb,CACd,EAEG,GADAlF,EAAE,MAAM,IAAI,kBAAmB4G,CAAG,EAC9B,CAACA,EAAI,QACR,MAAM,IAAI,MAAM,iBAAmBA,EAAI,SAAW,mBAAmB,EAEtE,OAAAA,EAAI,OAAS5G,EAAE,SAAS4G,EAAI,KAAMA,EAAI,OAAO,EAC7C5G,EAAE,MAAM,IAAI,iBAAkB4G,CAAG,EAC1BnC,EAAM,UAAUzE,EAAE,KAAK,OAAO4G,EAAI,MAAM,EAAGA,EAAI,QAAQ,CAC9D,EA0BD,SAAU,SAAUO,EAAMpB,EAAS,CAClC,IAAIqB,EAAOrB,EAAQ,KACnB,GAAIqB,EAAM,CACT,QAASnB,KAASmB,EACjBrB,EAAQE,GAASmB,EAAKnB,GAGvB,OAAOF,EAAQ,IACf,CAED,IAAIsB,EAAY,IAAIC,EACpB,OAAAC,EAASF,EAAWA,EAAU,KAAMF,CAAI,EAExCK,EAAaL,EAAME,EAAWtB,EAASsB,EAAU,KAAM,CAAC,EAEjDI,EAAQJ,CAAS,CACxB,EAOD,MAAO,CACN,IAAK,CAAE,EAcP,IAAK,SAAUnF,EAAMmE,EAAU,CAC9B,IAAIqB,EAAQ1H,EAAE,MAAM,IAEpB0H,EAAMxF,GAAQwF,EAAMxF,IAAS,CAAA,EAE7BwF,EAAMxF,GAAM,KAAKmE,CAAQ,CACzB,EAWD,IAAK,SAAUnE,EAAM0E,EAAK,CACzB,IAAIe,EAAY3H,EAAE,MAAM,IAAIkC,GAE5B,GAAI,GAACyF,GAAa,CAACA,EAAU,QAI7B,QAAS9D,EAAI,EAAGwC,EAAWA,EAAWsB,EAAU9D,MAC/CwC,EAASO,CAAG,CAEb,CACD,EAED,MAAOnC,CACT,EACCP,EAAM,MAAQlE,EAmBd,SAASyE,EAAM6B,EAAMsB,EAASC,EAAOC,EAAY,CAUhD,KAAK,KAAOxB,EASZ,KAAK,QAAUsB,EAQf,KAAK,MAAQC,EAEb,KAAK,QAAUC,GAAc,IAAI,OAAS,CAC1C,CA8BDrD,EAAM,UAAY,SAASsD,EAAUzH,EAAG4E,EAAU,CACjD,GAAI,OAAO5E,GAAK,SACf,OAAOA,EAER,GAAI,MAAM,QAAQA,CAAC,EAAG,CACrB,IAAI0H,EAAI,GACR,OAAA1H,EAAE,QAAQ,SAAU8B,EAAG,CACtB4F,GAAKD,EAAU3F,EAAG8C,CAAQ,CAC9B,CAAI,EACM8C,CACP,CAED,IAAIpB,EAAM,CACT,KAAMtG,EAAE,KACR,QAASyH,EAAUzH,EAAE,QAAS4E,CAAQ,EACtC,IAAK,OACL,QAAS,CAAC,QAAS5E,EAAE,IAAI,EACzB,WAAY,CAAE,EACd,SAAU4E,CACb,EAEM+C,EAAU3H,EAAE,MACZ2H,IACC,MAAM,QAAQA,CAAO,EACxB,MAAM,UAAU,KAAK,MAAMrB,EAAI,QAASqB,CAAO,EAE/CrB,EAAI,QAAQ,KAAKqB,CAAO,GAI1BjI,EAAE,MAAM,IAAI,OAAQ4G,CAAG,EAEvB,IAAIsB,EAAa,GACjB,QAAShG,KAAQ0E,EAAI,WACpBsB,GAAc,IAAMhG,EAAO,MAAQ0E,EAAI,WAAW1E,IAAS,IAAI,QAAQ,KAAM,QAAQ,EAAI,IAG1F,MAAO,IAAM0E,EAAI,IAAM,WAAaA,EAAI,QAAQ,KAAK,GAAG,EAAI,IAAMsB,EAAa,IAAMtB,EAAI,QAAU,KAAOA,EAAI,IAAM,GACtH,EASC,SAASuB,EAAaC,EAASnF,EAAKkE,EAAMkB,EAAY,CACrDD,EAAQ,UAAYnF,EACpB,IAAIqF,EAAQF,EAAQ,KAAKjB,CAAI,EAC7B,GAAImB,GAASD,GAAcC,EAAM,GAAI,CAEpC,IAAIC,EAAmBD,EAAM,GAAG,OAChCA,EAAM,OAASC,EACfD,EAAM,GAAKA,EAAM,GAAG,MAAMC,CAAgB,CAC1C,CACD,OAAOD,CACP,CAgBD,SAASd,EAAaL,EAAME,EAAWtB,EAASyC,EAAWC,EAAUC,EAAS,CAC7E,QAASzC,KAASF,EACjB,GAAI,GAACA,EAAQ,eAAeE,CAAK,GAAK,CAACF,EAAQE,IAI/C,KAAI0C,EAAW5C,EAAQE,GACvB0C,EAAW,MAAM,QAAQA,CAAQ,EAAIA,EAAW,CAACA,CAAQ,EAEzD,QAASC,EAAI,EAAGA,EAAID,EAAS,OAAQ,EAAEC,EAAG,CACzC,GAAIF,GAAWA,EAAQ,OAASzC,EAAQ,IAAM2C,EAC7C,OAGD,IAAIC,EAAaF,EAASC,GACtBjD,EAASkD,EAAW,OACpBR,GAAa,CAAC,CAACQ,EAAW,WAC1BC,GAAS,CAAC,CAACD,EAAW,OACtBhB,GAAQgB,EAAW,MAEvB,GAAIC,IAAU,CAACD,EAAW,QAAQ,OAAQ,CAEzC,IAAIE,GAAQF,EAAW,QAAQ,SAAU,EAAC,MAAM,WAAW,EAAE,GAC7DA,EAAW,QAAU,OAAOA,EAAW,QAAQ,OAAQE,GAAQ,GAAG,CAClE,CAKD,QAFIX,GAAUS,EAAW,SAAWA,EAG/BG,EAAcR,EAAU,KAAMvF,EAAMwF,EACxCO,IAAgB3B,EAAU,MAItB,EAAAqB,GAAWzF,GAAOyF,EAAQ,OAH9BzF,GAAO+F,EAAY,MAAM,OAAQA,EAAcA,EAAY,KAC1D,CAMD,IAAI7I,EAAM6I,EAAY,MAEtB,GAAI3B,EAAU,OAASF,EAAK,OAE3B,OAGD,GAAI,EAAAhH,aAAesE,GAInB,KAAIwE,EAAc,EACdX,EAEJ,GAAIQ,GAAQ,CAEX,GADAR,EAAQH,EAAaC,GAASnF,EAAKkE,EAAMkB,EAAU,EAC/C,CAACC,GAASA,EAAM,OAASnB,EAAK,OACjC,MAGD,IAAI+B,EAAOZ,EAAM,MACba,GAAKb,EAAM,MAAQA,EAAM,GAAG,OAC5Bc,EAAInG,EAIR,IADAmG,GAAKJ,EAAY,MAAM,OAChBE,GAAQE,GACdJ,EAAcA,EAAY,KAC1BI,GAAKJ,EAAY,MAAM,OAOxB,GAJAI,GAAKJ,EAAY,MAAM,OACvB/F,EAAMmG,EAGFJ,EAAY,iBAAiBvE,EAChC,SAID,QACKzD,EAAIgI,EACRhI,IAAMqG,EAAU,OAAS+B,EAAID,IAAM,OAAOnI,EAAE,OAAU,UACtDA,EAAIA,EAAE,KAENiI,IACAG,GAAKpI,EAAE,MAAM,OAEdiI,IAGA9I,EAAMgH,EAAK,MAAMlE,EAAKmG,CAAC,EACvBd,EAAM,OAASrF,CACrB,SACMqF,EAAQH,EAAaC,GAAS,EAAGjI,EAAKkI,EAAU,EAC5C,CAACC,EACJ,SAKF,IAAIY,EAAOZ,EAAM,MACbe,EAAWf,EAAM,GACjB1C,EAASzF,EAAI,MAAM,EAAG+I,CAAI,EAC1BI,GAAQnJ,EAAI,MAAM+I,EAAOG,EAAS,MAAM,EAExCE,EAAQtG,EAAM9C,EAAI,OAClBuI,GAAWa,EAAQb,EAAQ,QAC9BA,EAAQ,MAAQa,GAGjB,IAAIC,EAAaR,EAAY,KAEzBpD,IACH4D,EAAajC,EAASF,EAAWmC,EAAY5D,CAAM,EACnD3C,GAAO2C,EAAO,QAGf6D,EAAYpC,EAAWmC,EAAYP,CAAW,EAE9C,IAAIS,GAAU,IAAIjF,EAAMwB,EAAON,EAAS3F,EAAE,SAASqJ,EAAU1D,CAAM,EAAI0D,EAAUxB,GAAOwB,CAAQ,EAOhG,GANAL,EAAczB,EAASF,EAAWmC,EAAYE,EAAO,EAEjDJ,IACH/B,EAASF,EAAW2B,EAAaM,EAAK,EAGnCL,EAAc,EAAG,CAKpB,IAAIU,EAAgB,CACnB,MAAO1D,EAAQ,IAAM2C,EACrB,MAAOW,CACd,EACM/B,EAAaL,EAAME,EAAWtB,EAASiD,EAAY,KAAM/F,EAAK0G,CAAa,EAGvEjB,GAAWiB,EAAc,MAAQjB,EAAQ,QAC5CA,EAAQ,MAAQiB,EAAc,MAE/B,EACD,CACD,EAEF,CAeD,SAASrC,GAAa,CAErB,IAAIsC,EAAO,CAAE,MAAO,KAAM,KAAM,KAAM,KAAM,MAExCC,EAAO,CAAE,MAAO,KAAM,KAAMD,EAAM,KAAM,MAC5CA,EAAK,KAAOC,EAGZ,KAAK,KAAOD,EAEZ,KAAK,KAAOC,EACZ,KAAK,OAAS,CACd,CAWD,SAAStC,EAASuC,EAAMC,EAAM5H,EAAO,CAEpC,IAAI6H,EAAOD,EAAK,KAEZE,EAAU,CAAE,MAAO9H,EAAO,KAAM4H,EAAM,KAAMC,GAChD,OAAAD,EAAK,KAAOE,EACZD,EAAK,KAAOC,EACZH,EAAK,SAEEG,CACP,CASD,SAASR,EAAYK,EAAMC,EAAMG,EAAO,CAEvC,QADIF,EAAOD,EAAK,KACPlG,EAAI,EAAGA,EAAIqG,GAASF,IAASF,EAAK,KAAMjG,IAChDmG,EAAOA,EAAK,KAEbD,EAAK,KAAOC,EACZA,EAAK,KAAOD,EACZD,EAAK,QAAUjG,CACf,CAMD,SAAS4D,EAAQqC,EAAM,CAGtB,QAFIK,EAAQ,CAAA,EACRJ,EAAOD,EAAK,KAAK,KACdC,IAASD,EAAK,MACpBK,EAAM,KAAKJ,EAAK,KAAK,EACrBA,EAAOA,EAAK,KAEb,OAAOI,CACP,CAGD,GAAI,CAACjG,EAAM,SACV,OAAKA,EAAM,mBAKNlE,EAAE,6BAENkE,EAAM,iBAAiB,UAAW,SAAUgD,EAAK,CAChD,IAAIkD,EAAU,KAAK,MAAMlD,EAAI,IAAI,EAC7B9C,EAAOgG,EAAQ,SACftD,EAAOsD,EAAQ,KACfC,EAAiBD,EAAQ,eAE7BlG,EAAM,YAAYlE,EAAE,UAAU8G,EAAM9G,EAAE,UAAUoE,GAAOA,CAAI,CAAC,EACxDiG,GACHnG,EAAM,MAAK,CAEZ,EAAE,EAAK,GAGFlE,EAIR,IAAIsK,EAAStK,EAAE,KAAK,cAAa,EAE7BsK,IACHtK,EAAE,SAAWsK,EAAO,IAEhBA,EAAO,aAAa,aAAa,IACpCtK,EAAE,OAAS,KAIb,SAASuK,GAAiC,CACpCvK,EAAE,QACNA,EAAE,aAAY,CAEf,CAED,GAAI,CAACA,EAAE,OAAQ,CAOd,IAAIwK,EAAa,SAAS,WACtBA,IAAe,WAAaA,IAAe,eAAiBF,GAAUA,EAAO,MAChF,SAAS,iBAAiB,mBAAoBC,CAA8B,EAExE,OAAO,sBACV,OAAO,sBAAsBA,CAA8B,EAE3D,OAAO,WAAWA,EAAgC,EAAE,CAGtD,CAED,OAAOvK,CAER,EAAEkE,CAAK,EAE8BuG,EAAO,UAC3CA,EAAA,QAAiBtG,GAId,OAAOuG,GAAW,MACrBA,GAAO,MAAQvG,GAyDhBA,EAAM,UAAU,OAAS,CACxB,QAAW,CACV,QAAS,8BACT,OAAQ,EACR,EACD,OAAU,CACT,QAAS,iBACT,OAAQ,EACR,EACD,QAAW,CAEV,QAAS,uHACT,OAAQ,GACR,OAAQ,CACP,kBAAmB,CAClB,QAAS,6BACT,WAAY,GACZ,OAAQ,GACR,OAAQ,IACR,EACD,OAAU,CACT,QAAS,kBACT,OAAQ,EACR,EACD,YAAe,eACf,cAAe,YACf,KAAQ,YACR,CACD,EACD,MAAS,CACR,QAAS,4BACT,OAAQ,EACR,EACD,IAAO,CACN,QAAS,uHACT,OAAQ,GACR,OAAQ,CACP,IAAO,CACN,QAAS,iBACT,OAAQ,CACP,YAAe,QACf,UAAa,cACb,CACD,EACD,eAAgB,CAAE,EAClB,aAAc,CACb,QAAS,qCACT,OAAQ,CACP,YAAe,CACd,CACC,QAAS,KACT,MAAO,aACP,EACD,CACC,QAAS,mBACT,WAAY,EACZ,CACD,CACD,CACD,EACD,YAAe,OACf,YAAa,CACZ,QAAS,YACT,OAAQ,CACP,UAAa,cACb,CACD,CAED,CACD,EACD,OAAU,CACT,CACC,QAAS,kBACT,MAAO,cACP,EACD,oBACA,CACF,EAEAA,EAAM,UAAU,OAAO,IAAO,OAAO,cAAc,OAAO,OACzDA,EAAM,UAAU,OAAO,OACxBA,EAAM,UAAU,OAAO,QAAW,OAAO,mBAAmB,OAASA,EAAM,UAAU,OAGrFA,EAAM,MAAM,IAAI,OAAQ,SAAUyC,EAAK,CAElCA,EAAI,OAAS,WAChBA,EAAI,WAAW,MAAWA,EAAI,QAAQ,QAAQ,QAAS,GAAG,EAE5D,CAAC,EAED,OAAO,eAAezC,EAAM,UAAU,OAAO,IAAK,aAAc,CAY/D,MAAO,SAAoBwG,EAASvG,EAAM,CACzC,IAAIwG,EAAsB,CAAA,EAC1BA,EAAoB,YAAcxG,GAAQ,CACzC,QAAS,oCACT,WAAY,GACZ,OAAQD,EAAM,UAAUC,EAC3B,EACEwG,EAAoB,MAAW,uBAE/B,IAAIjF,EAAS,CACZ,iBAAkB,CACjB,QAAS,4BACT,OAAQiF,CACR,CACJ,EACEjF,EAAO,YAAcvB,GAAQ,CAC5B,QAAS,UACT,OAAQD,EAAM,UAAUC,EAC3B,EAEE,IAAIyG,EAAM,CAAA,EACVA,EAAIF,GAAW,CACd,QAAS,OAAO,wFAAwF,OAAO,QAAQ,MAAO,UAAY,CAAE,OAAOA,CAAU,CAAA,EAAG,GAAG,EACnK,WAAY,GACZ,OAAQ,GACR,OAAQhF,CACX,EAEExB,EAAM,UAAU,aAAa,SAAU,QAAS0G,CAAG,CACnD,CACF,CAAC,EACD,OAAO,eAAe1G,EAAM,UAAU,OAAO,IAAK,eAAgB,CAYjE,MAAO,SAAU2G,EAAU1G,EAAM,CAChCD,EAAM,UAAU,OAAO,IAAI,OAAO,gBAAgB,KAAK,CACtD,QAAS,OACR,aAAa,OAAS,MAAQ2G,EAAW,IAAM,iDAAiD,OAChG,GACA,EACD,WAAY,GACZ,OAAQ,CACP,YAAa,WACb,aAAc,CACb,QAAS,WACT,OAAQ,CACP,MAAS,CACR,QAAS,yCACT,WAAY,GACZ,MAAO,CAAC1G,EAAM,YAAcA,CAAI,EAChC,OAAQD,EAAM,UAAUC,EACxB,EACD,YAAe,CACd,CACC,QAAS,KACT,MAAO,aACP,EACD,KACA,CACD,CACD,CACD,CACJ,CAAG,CACD,CACF,CAAC,EAEDD,EAAM,UAAU,KAAOA,EAAM,UAAU,OACvCA,EAAM,UAAU,OAASA,EAAM,UAAU,OACzCA,EAAM,UAAU,IAAMA,EAAM,UAAU,OAEtCA,EAAM,UAAU,IAAMA,EAAM,UAAU,OAAO,SAAU,CAAA,CAAE,EACzDA,EAAM,UAAU,KAAOA,EAAM,UAAU,IACvCA,EAAM,UAAU,KAAOA,EAAM,UAAU,IACvCA,EAAM,UAAU,IAAMA,EAAM,UAAU,IAOrC,SAAUA,EAAO,CAEjB,IAAI4G,EAAS,8EAEb5G,EAAM,UAAU,IAAM,CACrB,QAAW,mBACX,OAAU,CACT,QAAS,OAAO,aAAe,sBAAsB,OAAS,IAAM4G,EAAO,OAAS,MAAQ,kBAAkB,MAAM,EACpH,OAAQ,CACP,KAAQ,WACR,6BAA8B,CAC7B,QAAS,4FACT,WAAY,GACZ,MAAO,UACP,EACD,QAAW,CACV,QAAS,yCACT,WAAY,EACZ,CAED,CACD,EACD,IAAO,CAEN,QAAS,OAAO,eAAiBA,EAAO,OAAS,IAAM,8BAA8B,OAAS,OAAQ,GAAG,EACzG,OAAQ,GACR,OAAQ,CACP,SAAY,QACZ,YAAe,UACf,OAAU,CACT,QAAS,OAAO,IAAMA,EAAO,OAAS,GAAG,EACzC,MAAO,KACP,CACD,CACD,EACD,SAAY,CACX,QAAS,OAAO,oDAAuDA,EAAO,OAAS,eAAe,EACtG,WAAY,EACZ,EACD,OAAU,CACT,QAASA,EACT,OAAQ,EACR,EACD,SAAY,CACX,QAAS,oFACT,WAAY,EACZ,EACD,UAAa,gBACb,SAAY,CACX,QAAS,kCACT,WAAY,EACZ,EACD,YAAe,WACjB,EAEC5G,EAAM,UAAU,IAAI,OAAU,OAAO,KAAOA,EAAM,UAAU,IAE5D,IAAI6G,EAAS7G,EAAM,UAAU,OACzB6G,IACHA,EAAO,IAAI,WAAW,QAAS,KAAK,EACpCA,EAAO,IAAI,aAAa,QAAS,KAAK,EAGvC,EAAC7G,CAAK,EAOPA,EAAM,UAAU,MAAQ,CACvB,QAAW,CACV,CACC,QAAS,kCACT,WAAY,GACZ,OAAQ,EACR,EACD,CACC,QAAS,mBACT,WAAY,GACZ,OAAQ,EACR,CACD,EACD,OAAU,CACT,QAAS,iDACT,OAAQ,EACR,EACD,aAAc,CACb,QAAS,2FACT,WAAY,GACZ,OAAQ,CACP,YAAe,OACf,CACD,EACD,QAAW,6GACX,QAAW,qBACX,SAAY,cACZ,OAAU,4DACV,SAAY,+CACZ,YAAe,eAChB,EAOAA,EAAM,UAAU,WAAaA,EAAM,UAAU,OAAO,QAAS,CAC5D,aAAc,CACbA,EAAM,UAAU,MAAM,cACtB,CACC,QAAS,0GACT,WAAY,EACZ,CACD,EACD,QAAW,CACV,CACC,QAAS,uBACT,WAAY,EACZ,EACD,CACC,QAAS,mdACT,WAAY,EACZ,CACD,EAED,SAAY,oGACZ,OAAU,CACT,QAAS,OACR,aAAa,OACb,OAGC,eAAe,OACf,IAEA,0BAA0B,OAC1B,IAEA,4BAA4B,OAC5B,IAEA,sCAAsC,OACtC,IAEA,gBAAgB,OAChB,IAEA,oFAAoF,QAErF,IACA,YAAY,MACZ,EACD,WAAY,EACZ,EACD,SAAY,2FACb,CAAC,EAEDA,EAAM,UAAU,WAAW,cAAc,GAAG,QAAU,uEAEtDA,EAAM,UAAU,aAAa,aAAc,UAAW,CACrD,MAAS,CACR,QAAS,OAGR,0DAA0D,OAK1D,KAAK,OACL,MACA,iEAAiE,OACjE,IAEA,qIAAqI,OACrI,IAEA,kEAAkE,MAClE,EACD,WAAY,GACZ,OAAQ,GACR,OAAQ,CACP,eAAgB,CACf,QAAS,4BACT,WAAY,GACZ,MAAO,iBACP,OAAQA,EAAM,UAAU,KACxB,EACD,kBAAmB,UACnB,cAAe,UACf,CACD,EAED,oBAAqB,CACpB,QAAS,gMACT,MAAO,UACP,EACD,UAAa,CACZ,CACC,QAAS,sIACT,WAAY,GACZ,OAAQA,EAAM,UAAU,UACxB,EACD,CACC,QAAS,qFACT,WAAY,GACZ,OAAQA,EAAM,UAAU,UACxB,EACD,CACC,QAAS,kEACT,WAAY,GACZ,OAAQA,EAAM,UAAU,UACxB,EACD,CACC,QAAS,8eACT,WAAY,GACZ,OAAQA,EAAM,UAAU,UACxB,CACD,EACD,SAAY,2BACb,CAAC,EAEDA,EAAM,UAAU,aAAa,aAAc,SAAU,CACpD,SAAY,CACX,QAAS,QACT,OAAQ,GACR,MAAO,SACP,EACD,kBAAmB,CAClB,QAAS,2EACT,OAAQ,GACR,OAAQ,CACP,uBAAwB,CACvB,QAAS,QACT,MAAO,QACP,EACD,cAAiB,CAChB,QAAS,mEACT,WAAY,GACZ,OAAQ,CACP,4BAA6B,CAC5B,QAAS,YACT,MAAO,aACP,EACD,KAAMA,EAAM,UAAU,UACtB,CACD,EACD,OAAU,SACV,CACD,EACD,kBAAmB,CAClB,QAAS,4EACT,WAAY,GACZ,OAAQ,GACR,MAAO,UACP,CACF,CAAC,EAEDA,EAAM,UAAU,aAAa,aAAc,WAAY,CACtD,mBAAoB,CACnB,QAAS,oFACT,WAAY,GACZ,MAAO,UACP,CACF,CAAC,EAEGA,EAAM,UAAU,SACnBA,EAAM,UAAU,OAAO,IAAI,WAAW,SAAU,YAAY,EAI5DA,EAAM,UAAU,OAAO,IAAI,aAC1B,yNAAyN,OACzN,YACF,GAGAA,EAAM,UAAU,GAAKA,EAAM,UAAU,WAOpC,UAAY,CAEZ,GAAI,OAAOA,EAAU,KAAe,OAAO,SAAa,IACvD,OAII,QAAQ,UAAU,UACtB,QAAQ,UAAU,QAAU,QAAQ,UAAU,mBAAqB,QAAQ,UAAU,uBAGtF,IAAI8G,EAAkB,gBAClBC,EAAkB,SAAUC,EAAQf,EAAS,CAChD,MAAO,gBAAae,EAAS,yBAA2Bf,CAC1D,EACKgB,EAAwB,gDAExBC,EAAa,CAChB,GAAM,aACN,GAAM,SACN,GAAM,OACN,IAAO,aACP,KAAQ,aACR,GAAM,OACN,IAAO,QACP,EAAK,IACL,IAAO,OACT,EAEKC,EAAc,kBACdC,EAAiB,UACjBC,EAAgB,SAChBC,EAAgB,SAEhBC,EAAW,sBAAwBJ,EAAc,KAAOE,EAAgB,YAC9DF,EAAc,KAAOC,EAAiB,MASpD,SAASI,EAASvG,EAAKwG,EAASC,EAAO,CACtC,IAAIC,EAAM,IAAI,eACdA,EAAI,KAAK,MAAO1G,EAAK,EAAI,EACzB0G,EAAI,mBAAqB,UAAY,CAChCA,EAAI,YAAc,IACjBA,EAAI,OAAS,KAAOA,EAAI,aAC3BF,EAAQE,EAAI,YAAY,EAEpBA,EAAI,QAAU,IACjBD,EAAMX,EAAgBY,EAAI,OAAQA,EAAI,UAAU,CAAC,EAEjDD,EAAMT,CAAqB,EAIjC,EACEU,EAAI,KAAK,IAAI,CACb,CAUD,SAASC,EAAWC,EAAO,CAC1B,IAAI/G,EAAI,wCAAwC,KAAK+G,GAAS,EAAE,EAChE,GAAI/G,EAAG,CACN,IAAI/B,EAAQ,OAAO+B,EAAE,EAAE,EACnBgH,EAAQhH,EAAE,GACViH,EAAMjH,EAAE,GAEZ,OAAKgH,EAGAC,EAGE,CAAChJ,EAAO,OAAOgJ,CAAG,CAAC,EAFlB,CAAChJ,EAAO,MAAS,EAHjB,CAACA,EAAOA,CAAK,CAMrB,CAED,CAEDiB,EAAM,MAAM,IAAI,sBAAuB,SAAUyC,EAAK,CACrDA,EAAI,UAAY,KAAO8E,CACzB,CAAE,EAEDvH,EAAM,MAAM,IAAI,sBAAuB,SAAUyC,EAAK,CACrD,IAAIuF,EAAqCvF,EAAI,QAC7C,GAAIuF,EAAI,QAAQT,CAAQ,EAAG,CAC1B9E,EAAI,KAAO,GAEXuF,EAAI,aAAab,EAAaC,CAAc,EAG5C,IAAIzE,EAAOqF,EAAI,YAAY,SAAS,cAAc,MAAM,CAAC,EACzDrF,EAAK,YAAcmE,EAEnB,IAAI7F,EAAM+G,EAAI,aAAa,UAAU,EAEjCjH,EAAW0B,EAAI,SACnB,GAAI1B,IAAa,OAAQ,CAGxB,IAAIkH,GAAa,WAAW,KAAKhH,CAAG,GAAK,CAAG,CAAA,MAAM,GAAG,GACrDF,EAAWmG,EAAWe,IAAcA,CACpC,CAGDjI,EAAM,KAAK,YAAY2C,EAAM5B,CAAQ,EACrCf,EAAM,KAAK,YAAYgI,EAAKjH,CAAQ,EAGpC,IAAImH,EAAalI,EAAM,QAAQ,WAC3BkI,GACHA,EAAW,cAAcnH,CAAQ,EAIlCyG,EACCvG,EACA,SAAU+B,EAAM,CAEfgF,EAAI,aAAab,EAAaE,CAAa,EAG3C,IAAIQ,EAAQD,EAAWI,EAAI,aAAa,YAAY,CAAC,EACrD,GAAIH,EAAO,CACV,IAAIM,EAAQnF,EAAK,MAAM,WAAW,EAG9BjE,EAAQ8I,EAAM,GACdE,EAAMF,EAAM,IAAM,KAAOM,EAAM,OAASN,EAAM,GAE9C9I,EAAQ,IAAKA,GAASoJ,EAAM,QAChCpJ,EAAQ,KAAK,IAAI,EAAG,KAAK,IAAIA,EAAQ,EAAGoJ,EAAM,MAAM,CAAC,EACjDJ,EAAM,IAAKA,GAAOI,EAAM,QAC5BJ,EAAM,KAAK,IAAI,EAAG,KAAK,IAAIA,EAAKI,EAAM,MAAM,CAAC,EAE7CnF,EAAOmF,EAAM,MAAMpJ,EAAOgJ,CAAG,EAAE,KAAK;AAAA,CAAI,EAGnCC,EAAI,aAAa,YAAY,GACjCA,EAAI,aAAa,aAAc,OAAOjJ,EAAQ,CAAC,CAAC,CAEjD,CAGD4D,EAAK,YAAcK,EACnBhD,EAAM,iBAAiB2C,CAAI,CAC3B,EACD,SAAU+E,EAAO,CAEhBM,EAAI,aAAab,EAAaG,CAAa,EAE3C3E,EAAK,YAAc+E,CACnB,CACL,CACG,CACH,CAAE,EAED1H,EAAM,QAAQ,cAAgB,CAQ7B,UAAW,SAAmBwC,EAAW,CAGxC,QAFI4F,GAAY5F,GAAa,UAAU,iBAAiB+E,CAAQ,EAEvD7H,EAAI,EAAGmB,EAAUA,EAAUuH,EAAS1I,MAC5CM,EAAM,iBAAiBa,CAAO,CAE/B,CACH,EAEC,IAAIwH,EAAS,GAEbrI,EAAM,cAAgB,UAAY,CAC5BqI,IACJ,QAAQ,KAAK,yFAAyF,EACtGA,EAAS,IAEVrI,EAAM,QAAQ,cAAc,UAAU,MAAM,KAAM,SAAS,CAC7D,CAEA,SCx5DA,MAAM,UAAU,KAAO,CACtB,SAAY,CACX,QAAS,yCACT,WAAY,GACZ,OAAQ,EACR,EACD,OAAU,CACT,QAAS,yCACT,WAAY,GACZ,OAAQ,EACR,EACD,QAAW,CACV,QAAS,gCACT,OAAQ,EACR,EACD,OAAU,qCACV,YAAe,WACf,SAAY,IACZ,QAAW,qBACX,KAAQ,CACP,QAAS,WACT,MAAO,SACP,CACF,EAEA,MAAM,UAAU,YAAc,MAAM,UAAU,KCtBvC,MAAMsI,EAAY,CAACC,EAAWxH,EAA2D,SAAU,CAClG,MAAA0C,EAAU1C,IAAa,OAAS,KAAK,UAAUwH,EAAM,KAAM,CAAC,EAAIA,EACtE,GAAIA,EAAM,CAGJ,IAAA5F,EAFc6F,EAAAA,QAAAA,UAAgB/E,EAASgF,EAAM,QAAA,UAAU1H,GAAWA,CAAQ,EAEzD,MAAM;AAAA,CAAI,EAC5B,IAAI,CAAC2H,EAAMC,IAAQ,4EAA4EA,EAAM,GAAG,WAAW,SAAS,EAAG,GAAG,aAAaD,GAAM,EACrJ,KAAK;AAAA,CAAI,EAIR,OAAA/I,EAAY8D,CAAO,IACrBd,EAAOA,EACJ,WAAW,2CAA4C,uHAAuH,EAC9K,WAAW,2CAA4C,yHAAyH,EAChL,WAAW,2CAA4C,iEAAiE,EACxH,WAAW,2CAA4C,iEAAiE,GAGtH,yBAAyB5B,MAAa4B,UAC9C,CAEM,MAAA,EAET,EC5BaiG,GAAc5M,GAAe,CACpC,GAAA,CAGK,OAFQ,IAAI,YACA,gBAAgBA,EAAK,iBAAiB,EAC9C,gBAAgB,WAAa,oBAEjC,MAAA,EACR,CACH,ECRM,SAAU6M,GAAY7M,EAAW,CACjC,GAAA,CAGK,OAFQ,IAAI,YACA,gBAAgBA,EAAK,WAAW,EACxC,gBAAgB,WAAa,aAEjC,MAAA,EACR,CACH,CCRM,SAAU8M,GAAY9M,EAAQ,CAClC,OAAOA,aAAe,IACxB,CCGM,SAAU+M,GAAU/M,EAAW,CAC/B,OAAA2D,EAAY3D,CAAG,EACV,OACE4M,GAAW5M,CAAG,EAChB,MACE6M,GAAY7M,CAAG,EACjB,OACE8M,GAAY9M,CAAG,EACjB,OAEA,WAEX,CCPO,MAAMgN,GAAiB,CAACC,EAAsB3M,EAA4B4M,EAAuBC,EAAeC,IAAqB,OAE1I,KAAM,CAAE,IAAAlM,EAAK,OAAAE,GAAWH,IAGlBoM,EAAM,QAAQ,IAAI,CACtB,KAAM/M,EAAQ,QAAU,MACxB,QAAS,GACT,QAAS,GAAGA,EAAQ,KAAA,CACrB,EAAE,SAAS,SAAS,EAEf,CAAE,KAAAiM,EAAM,OAAAvB,EAAQ,QAAAsC,EAAS,WAAAC,EAAY,SAAAC,CAAa,EAAAP,EAElDQ,EAAmB,GAAGzC,SAAgBuC,KACtCL,EAAAC,GAAO,OAASM,GAAoB,GAC1CP,EAAMC,GAAO,KAAOK,EACpB,MAAME,EAAoBJ,EAAQ,gBAC5BK,EAAsBL,EAAQ,kBAC9BM,EAAsBN,EAAQ,cAE9BO,EADO,OAAOtB,IACK,SAAW,KAAK,UAAUA,EAAM,KAAM,CAAC,EAAIA,EAEpE,GAAImB,EAAmB,CACrB,MAAMI,EAAcJ,EAAkB,MAAM,GAAG,EAAE,GAS3C3I,EARU,CACd,WAAY,MACZ,mBAAoB,OACpB,YAAa,OACb,aAAc,WAAA,EAEc+I,IAEIf,GAAUR,CAAI,EAEhDW,EAAMC,GAAO,aAAa,UAAYb,EAAUC,EAAMxH,CAAQ,EACxDmI,EAAAC,GAAO,aAAa,KAAOU,CAElC,CAGK,MAAAE,EAAexK,EAAAA,QAAU,MAAMqK,EAAqB,CACxD,aAAc,EAAA,CACf,EAEKV,EAAAC,GAAO,QAAQ,KAAOY,EAGvBb,EAAMC,GAAO,YAAY,UAAU,SAChCD,EAAAC,GAAO,YAAY,UAAY,uEAIlCD,EAAMC,GAAO,aAAa,UAAU,SACjCD,EAAAC,GAAO,aAAa,UAAY,uEAIlCD,EAAAC,GAAO,gBAAgB,KAAOG,EACpCJ,EAAMC,GAAO,gBAAgB,UAAYb,EAAUgB,CAAO,EAGpD,MAAA7J,EAAOkK,EAAsB,SAASA,CAAmB,EAAI/J,IAAcnD,EAAAyM,EAAMC,GAAO,aAAa,OAA1B,YAAA1M,EAAgC,KAAK,EAChHyM,EAAAC,GAAO,KAAO3J,GAAYC,CAAI,EACpCwJ,EAAI,KAAOxJ,EAEX,MAAMuK,EAAUf,EAEVgB,EAAsB,IACnB,QAAQ,EAAE,IAAIf,EAAMC,GAAO,KAAM,CAAE,IAAK,EAAO,CAAA,EAIrD,GAAA,OAAO,CAAE,IAAK,EAAO,CAAA,EACrB,KAAKc,CAAmB,EACxB,KAAMC,GAAO,OAGZ,OAAAb,EAAI,IAAI,CACN,cAAY,CACH,MAAA,CACL,QAAAW,CAAA,CAEJ,CAAA,CACD,EAGD,OAAO,MAAM5M,GAAU8L,EAEnBG,EAAA,IAAI,CAAE,IAAAa,CAAA,CAAK,EACXb,EAAA,SAAS,UAAU,EAAE,OAGrB5M,EAAAS,EAAA,eAAe,iBAAiB,IAAhC,MAAAT,EAAmC,iBAGnC,QAAQ,IAAI,cAAc,IAC5B2M,EAAI,QAAO,QAINH,CAAA,CAER,CACL,wOCjFQ,MAAAkB,EAAeC,IACH,CACd,OAAU,cACV,KAAQ,gBACR,IAAO,gBACP,IAAO,eACP,MAAS,iBACT,KAAQ,gBAAA,GAEKA,iqICCX,MAAAC,EAAerD,GAAmB,CACtC,MAAMsD,EAAiBtD,EAAO,UAAU,EAAE,CAAC,EAO3C,MANiB,CACf,EAAK,gBACL,EAAK,iBACL,EAAK,cACL,EAAK,aAAA,EAESsD,EAAA;;EC9CPC,GAAY,IAAK,CAEtB,KAAA,CAAE,IAAArN,GAAQD,IAEVwI,EAAOvI,EAAI,MAAQA,EAAI,qBAAqB,MAAM,EAAE,GAGpDI,EAAQJ,EAAI,cAAc,OAAO,EACjCI,EAAA,aAAa,KAAM,mBAAmB,EAC5CmI,EAAK,YAAYnI,CAAK,EACtBA,EAAM,YAAYJ,EAAI,eAAesN,EAAI,CAAC,EAGpC,MAAAC,GAAa,qBAAK,SAAS,cAAc,wBAAwB,qBAAK,SAAS,cAAc,SAC7FC,EAAkB,SAAS,cAAc,OAAO,EACtDD,GAAA,MAAAA,EAAY,YAAYC,GACxBA,EAAgB,YAAYxN,EAAI,eAAeyN,EAAQ,CAAC,CAE1D,EClBaC,GAAexB,GAAqB,CAEzC,KAAA,CAAE,IAAAlM,GAAQD,SAIV,MAAA0E,EAAOzE,EAAI,cAAc,KAAK,EAC/ByE,EAAA,aAAa,KAAM,iBAAiB,EACrCzE,EAAA,KAAK,YAAYyE,CAAI,EAEnB,MAAAkJ,EAAS3N,EAAI,eAAe,iBAAiB,EACnDkM,EAAI,MAAMyB,CAAiB,CAE7B,ECZM,CAAEhP,EAAAA,EAAM,EAAA,QAEDiP,GAAa,IAAK,OAE7B,MAAMC,EAAyB,CAC7B,GAAIlP,GAAE,SAAU,EAChB,OAAQ,MACR,OAAQ,GACR,KAAM,EACN,KAAM,GACN,IAAK,GACL,KAAM,CACJ,KAAM,CAAE,EACR,UAAW,EACZ,EACD,MAAO,CACL,KAAM,CAAE,EACR,UAAW,EACZ,EACD,eAAgB,CACd,KAAM,CAAE,EACR,UAAW,EACZ,EACD,YAAa,CACX,KAAM,CAAE,EACR,UAAW,EACZ,EACD,aAAc,CACZ,KAAM,CAAE,EACR,UAAW,EACZ,EACD,gBAAiB,CACf,KAAM,CAAE,EACR,UAAW,EACZ,EACD,QAAS,CACP,KAAM,CAAE,CACT,CAAA,EAGG,CAAE,IAAAqB,EAAK,QAAAC,EAAS,OAAAC,GAAWH,EAAQ,EAGnC+N,EAAU7N,IAAY,EACtB8N,EAAe/N,EAAI,MAAQ,cAG3BgO,EAAa,IAAAzO,EAAA,OAAO,MAAMW,KAAb,MAAAX,EAAsB,QAGnC0O,EAA+BD,GAAc,CAACF,EAAU,OAAO,MAAM5N,GAAU,GAGrF+N,EAAa,KAAKJ,CAAQ,EAGpB,MAAA7B,EAAQkC,WAASD,CAAY,EAE7B/B,EAAMiC,YAAUC,GAAK,CACzB,MAAApC,CAAA,CACD,EAID,OAAI,CAACgC,GAAcF,GAAW,QAAQ,IAAI,cAAc,GAAKC,IAC3DL,GAAYxB,CAAG,EAGV,CAAE,IAAAA,EAAK,MAAAF,EAEhB,ECxEaqC,GAAgB,CAACrC,EAAuBC,IAAiB,CAGpED,EAAMC,GAAO,YAAY,UAAYb,EAAUY,EAAMC,GAAO,YAAY,IAAI,EAE5ED,EAAMC,GAAO,eAAe,UAAYb,EAAUY,EAAMC,GAAO,eAAe,IAAI,EAElFD,EAAMC,GAAO,MAAM,UAAYb,EAAUY,EAAMC,GAAO,MAAM,IAAI,EAEhED,EAAMC,GAAO,KAAK,UAAYb,EAAUY,EAAMC,GAAO,KAAK,IAAI,CAEhE,ECdaqC,GAAcxP,GAAqB,CAC1C,GAAA,CACK,OAAA,IAAI,IAAIA,CAAG,QAEX,OAAA,IACR,CACH,ECNayP,GAAazP,GACjB,wKACJ,KAAKA,CAAG,ECCA0P,GAAkB1P,GACtBwP,GAAWxP,CAAG,GAAKyP,GAAUzP,CAAG,ECFnC,CAAE,EAAAH,CAAM,EAAA,QAED8P,GAAa,CAACzC,EAAuBC,EAAe7M,IAA8B,CAC7F4M,EAAMC,GAAO,OAAStN,EAAE,UAAUS,EAAQ,MAAM,GAAK,MACrD4M,EAAMC,GAAO,IAAMuC,GAAepP,EAAQ,GAAG,EAAIA,EAAQ,IAAM,QAAQ,OAAO,SAAS,EAAIA,EAAQ,IACnG4M,EAAMC,GAAO,MAAM,KAAOtN,EAAE,UAAUS,EAAQ,EAAE,EAChD4M,EAAMC,GAAO,KAAK,KAAOtN,EAAE,UAAUS,EAAQ,IAAI,EACjD4M,EAAMC,GAAO,eAAe,KAAOtN,EAAE,UAAUS,EAAQ,OAAO,EAC9D4M,EAAMC,GAAO,YAAY,KAAOtN,EAAE,UAAUS,EAAQ,IAAI,CAC1D,ECDMsP,GAAY,GAAG,QAAQ,KAAK,CAAE,CAAA,EAEvBC,GAAM,IAAIC,IAAwC,CAE7D,KAAM,CAAE,MAAA5C,EAAO,IAAAE,GAAQ0B,KACjBxO,EAA6BL,GAAe,GAAG6P,CAAM,EACrD3C,EAAQD,EAAM,OAAS,EAClB,OAAAyC,GAAAzC,EAAOC,EAAO7M,CAAO,EAE5B,QAAQ,IAAI,iBAAiB,IAAS4M,EAAAC,GAAS9M,GAAU6M,EAAMC,EAAM,GACzEoC,GAAcrC,EAAOC,CAAK,EAEnByC,GAAU,CAAE,GAAGtP,EAAS,IAAK,EAAO,CAAA,EAAE,KAAK2M,GAAOD,GAAeC,EAAK3M,EAAS4M,EAAOC,EAAOC,CAAG,CAAC,CAC1G,EClBA,OAAO,IAAK,CAEV,OAAO,MAAQ,EACjB,CAAC,EAED,QAAQ,SAAS,OAAO,CAAE,IAAAyC,EAAK,CAAA,EAE/B,QAAQ,SAAS,UAAU,UAAW,CAACE,KAAe7P,IAChD,QAAQ,IAAI,aAAa,EACpB2P,GAAI,GAAG3P,CAAI,EAEX6P,EAAW,GAAG7P,CAAI,CAE5B"}