"use strict";const a=require("vue"),{_:ne}=Cypress,pe=["GET","POST","PUT","HEAD","DELETE","OPTIONS","TRACE","COPY","LOCK","<PERSON>KCOL","MOVE","PURGE","<PERSON>OPFIND","PROPPATCH","UNLOCK","REPORT","MKACTIVITY","CHECKOUT","MERGE","M-SEARCH","NOTIFY","SUBSCRIBE","UNSUBSCRIBE","PATCH","SEARCH","CONNECT"],me=e=>ne.isString(e)&&ne.includes(pe,e.toUpperCase()),{_:Z}=Cypress,ge=(...e)=>{Z.isFunction(e[0])&&e.shift();const n={},t=n;return Z.isObject(e[0])?Z.extend(t,e[0]):e.length===1?n.url=e[0]:e.length===2?me(e[0])?(n.method=e[0],n.url=e[1]):(n.url=e[0],n.body=e[1]):e.length===3&&(n.method=e[0],n.url=e[1],n.body=e[2]),t},he=e=>{var i,u,h,f,s,b,v;const n=Cypress.env("hideCredentialsOptions")===void 0,t={auth:[],body:[],headers:[],qs:[],...Cypress.env("hideCredentialsOptions")};return n&&((i=t.auth)==null||i.push("user","username","pass","password","bearer"),(u=t.headers)==null||u.push("authorization","Authorization","password","username"),(h=t.body)==null||h.push("pass","password")),(f=t.auth)==null||f.forEach(g=>{e.auth.body&&e.auth.body[g]&&(e.auth.body[g]=e==null?void 0:e.auth.body[g].replace(/./g,"*"))}),(s=t.headers)==null||s.forEach(g=>{e.requestHeaders.body&&e.requestHeaders.body[g]&&(e.requestHeaders.body[g]=e==null?void 0:e.requestHeaders.body[g].replace(/./g,"*"))}),(b=t.body)==null||b.forEach(g=>{e.requestBody.body&&e.requestBody.body[g]&&(e.requestBody.body[g]=e==null?void 0:e.requestBody.body[g].replace(/./g,"*"))}),(v=t.qs)==null||v.forEach(g=>{e.query.body&&e.query.body[g]&&(e.query.body[g]=e==null?void 0:e.query.body[g].replace(/./g,"*"))}),e},M=()=>{const e=cy.state("document"),n=cy.state("runnable")._currentRetry,t=cy.state("test").id;return{doc:e,attempt:n,testId:t}},ye=()=>{const{doc:e}=M(),n=e.getElementById("api-plugin-styles");n==null||n.remove()};var ie=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},D={exports:{}},H={decodeValues:!0,map:!1,silent:!1};function J(e){return typeof e=="string"&&!!e.trim()}function X(e,n){var t=e.split(";").filter(J),i=t.shift(),u=be(i),h=u.name,f=u.value;n=n?Object.assign({},H,n):H;try{f=n.decodeValues?decodeURIComponent(f):f}catch(b){console.error("set-cookie-parser encountered an error while decoding a cookie with value '"+f+"'. Set options.decodeValues to false to disable this feature.",b)}var s={name:h,value:f};return t.forEach(function(b){var v=b.split("="),g=v.shift().trimLeft().toLowerCase(),F=v.join("=");g==="expires"?s.expires=new Date(F):g==="max-age"?s.maxAge=parseInt(F,10):g==="secure"?s.secure=!0:g==="httponly"?s.httpOnly=!0:g==="samesite"?s.sameSite=F:s[g]=F}),s}function be(e){var n="",t="",i=e.split("=");return i.length>1?(n=i.shift(),t=i.join("=")):t=e,{name:n,value:t}}function se(e,n){if(n=n?Object.assign({},H,n):H,!e)return n.map?{}:[];if(e.headers&&e.headers["set-cookie"])e=e.headers["set-cookie"];else if(e.headers){var t=e.headers[Object.keys(e.headers).find(function(u){return u.toLowerCase()==="set-cookie"})];!t&&e.headers.cookie&&!n.silent&&console.warn("Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning."),e=t}if(Array.isArray(e)||(e=[e]),n=n?Object.assign({},H,n):H,n.map){var i={};return e.filter(J).reduce(function(u,h){var f=X(h,n);return u[f.name]=f,u},i)}else return e.filter(J).map(function(u){return X(u,n)})}function fe(e){if(Array.isArray(e))return e;if(typeof e!="string")return[];var n=[],t=0,i,u,h,f,s;function b(){for(;t<e.length&&/\s/.test(e.charAt(t));)t+=1;return t<e.length}function v(){return u=e.charAt(t),u!=="="&&u!==";"&&u!==","}for(;t<e.length;){for(i=t,s=!1;b();)if(u=e.charAt(t),u===","){for(h=t,t+=1,b(),f=t;t<e.length&&v();)t+=1;t<e.length&&e.charAt(t)==="="?(s=!0,t=f,n.push(e.substring(i,h)),i=t):t=h+1}else t+=1;(!s||t>=e.length)&&n.push(e.substring(i,e.length))}return n}D.exports=se;D.exports.parse=se;D.exports.parseString=X;D.exports.splitCookiesString=fe;function we(e){const n=e==0?0:Math.floor(Math.log(e)/Math.log(1024));return(e/Math.pow(1024,n)).toFixed(2)*1+"\xA0"+["B","kB","MB","GB","TB"][n]}const Q=e=>{if(typeof e=="object")return!0;if(typeof e=="string")try{return JSON.parse(e),!0}catch{return!1}else return!1},xe=e=>{if(!e)return 0;const t=e.toString().replace(/\r\n/g,`
`),i=Q(t)?t.replace(/\s/g,""):t;return new Blob([i]).size};var K={exports:{}};(function(e){var n=typeof window<"u"?window:typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope?self:{};/**
 * Prism: Lightweight, robust, elegant syntax highlighting
 *
 * @license MIT <https://opensource.org/licenses/MIT>
 * <AUTHOR> Verou <https://lea.verou.me>
 * @namespace
 * @public
 */var t=function(i){var u=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,h=0,f={},s={manual:i.Prism&&i.Prism.manual,disableWorkerMessageHandler:i.Prism&&i.Prism.disableWorkerMessageHandler,util:{encode:function o(r){return r instanceof b?new b(r.type,o(r.content),r.alias):Array.isArray(r)?r.map(o):r.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(o){return Object.prototype.toString.call(o).slice(8,-1)},objId:function(o){return o.__id||Object.defineProperty(o,"__id",{value:++h}),o.__id},clone:function o(r,l){l=l||{};var d,c;switch(s.util.type(r)){case"Object":if(c=s.util.objId(r),l[c])return l[c];d={},l[c]=d;for(var m in r)r.hasOwnProperty(m)&&(d[m]=o(r[m],l));return d;case"Array":return c=s.util.objId(r),l[c]?l[c]:(d=[],l[c]=d,r.forEach(function(y,p){d[p]=o(y,l)}),d);default:return r}},getLanguage:function(o){for(;o;){var r=u.exec(o.className);if(r)return r[1].toLowerCase();o=o.parentElement}return"none"},setLanguage:function(o,r){o.className=o.className.replace(RegExp(u,"gi"),""),o.classList.add("language-"+r)},currentScript:function(){if(typeof document>"u")return null;if("currentScript"in document&&1<2)return document.currentScript;try{throw new Error}catch(d){var o=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(d.stack)||[])[1];if(o){var r=document.getElementsByTagName("script");for(var l in r)if(r[l].src==o)return r[l]}return null}},isActive:function(o,r,l){for(var d="no-"+r;o;){var c=o.classList;if(c.contains(r))return!0;if(c.contains(d))return!1;o=o.parentElement}return!!l}},languages:{plain:f,plaintext:f,text:f,txt:f,extend:function(o,r){var l=s.util.clone(s.languages[o]);for(var d in r)l[d]=r[d];return l},insertBefore:function(o,r,l,d){d=d||s.languages;var c=d[o],m={};for(var y in c)if(c.hasOwnProperty(y)){if(y==r)for(var p in l)l.hasOwnProperty(p)&&(m[p]=l[p]);l.hasOwnProperty(y)||(m[y]=c[y])}var x=d[o];return d[o]=m,s.languages.DFS(s.languages,function(A,q){q===x&&A!=o&&(this[A]=m)}),m},DFS:function o(r,l,d,c){c=c||{};var m=s.util.objId;for(var y in r)if(r.hasOwnProperty(y)){l.call(r,y,r[y],d||y);var p=r[y],x=s.util.type(p);x==="Object"&&!c[m(p)]?(c[m(p)]=!0,o(p,l,null,c)):x==="Array"&&!c[m(p)]&&(c[m(p)]=!0,o(p,l,y,c))}}},plugins:{},highlightAll:function(o,r){s.highlightAllUnder(document,o,r)},highlightAllUnder:function(o,r,l){var d={callback:l,container:o,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};s.hooks.run("before-highlightall",d),d.elements=Array.prototype.slice.apply(d.container.querySelectorAll(d.selector)),s.hooks.run("before-all-elements-highlight",d);for(var c=0,m;m=d.elements[c++];)s.highlightElement(m,r===!0,d.callback)},highlightElement:function(o,r,l){var d=s.util.getLanguage(o),c=s.languages[d];s.util.setLanguage(o,d);var m=o.parentElement;m&&m.nodeName.toLowerCase()==="pre"&&s.util.setLanguage(m,d);var y=o.textContent,p={element:o,language:d,grammar:c,code:y};function x(q){p.highlightedCode=q,s.hooks.run("before-insert",p),p.element.innerHTML=p.highlightedCode,s.hooks.run("after-highlight",p),s.hooks.run("complete",p),l&&l.call(p.element)}if(s.hooks.run("before-sanity-check",p),m=p.element.parentElement,m&&m.nodeName.toLowerCase()==="pre"&&!m.hasAttribute("tabindex")&&m.setAttribute("tabindex","0"),!p.code){s.hooks.run("complete",p),l&&l.call(p.element);return}if(s.hooks.run("before-highlight",p),!p.grammar){x(s.util.encode(p.code));return}if(r&&i.Worker){var A=new Worker(s.filename);A.onmessage=function(q){x(q.data)},A.postMessage(JSON.stringify({language:p.language,code:p.code,immediateClose:!0}))}else x(s.highlight(p.code,p.grammar,p.language))},highlight:function(o,r,l){var d={code:o,grammar:r,language:l};if(s.hooks.run("before-tokenize",d),!d.grammar)throw new Error('The language "'+d.language+'" has no grammar.');return d.tokens=s.tokenize(d.code,d.grammar),s.hooks.run("after-tokenize",d),b.stringify(s.util.encode(d.tokens),d.language)},tokenize:function(o,r){var l=r.rest;if(l){for(var d in l)r[d]=l[d];delete r.rest}var c=new F;return C(c,c.head,o),g(o,c,r,c.head,0),B(c)},hooks:{all:{},add:function(o,r){var l=s.hooks.all;l[o]=l[o]||[],l[o].push(r)},run:function(o,r){var l=s.hooks.all[o];if(!(!l||!l.length))for(var d=0,c;c=l[d++];)c(r)}},Token:b};i.Prism=s;function b(o,r,l,d){this.type=o,this.content=r,this.alias=l,this.length=(d||"").length|0}b.stringify=function o(r,l){if(typeof r=="string")return r;if(Array.isArray(r)){var d="";return r.forEach(function(x){d+=o(x,l)}),d}var c={type:r.type,content:o(r.content,l),tag:"span",classes:["token",r.type],attributes:{},language:l},m=r.alias;m&&(Array.isArray(m)?Array.prototype.push.apply(c.classes,m):c.classes.push(m)),s.hooks.run("wrap",c);var y="";for(var p in c.attributes)y+=" "+p+'="'+(c.attributes[p]||"").replace(/"/g,"&quot;")+'"';return"<"+c.tag+' class="'+c.classes.join(" ")+'"'+y+">"+c.content+"</"+c.tag+">"};function v(o,r,l,d){o.lastIndex=r;var c=o.exec(l);if(c&&d&&c[1]){var m=c[1].length;c.index+=m,c[0]=c[0].slice(m)}return c}function g(o,r,l,d,c,m){for(var y in l)if(!(!l.hasOwnProperty(y)||!l[y])){var p=l[y];p=Array.isArray(p)?p:[p];for(var x=0;x<p.length;++x){if(m&&m.cause==y+","+x)return;var A=p[x],q=A.inside,te=!!A.lookbehind,ae=!!A.greedy,le=A.alias;if(ae&&!A.pattern.global){var de=A.pattern.toString().match(/[imsuy]*$/)[0];A.pattern=RegExp(A.pattern.source,de+"g")}for(var re=A.pattern||A,S=d.next,z=c;S!==r.tail&&!(m&&z>=m.reach);z+=S.value.length,S=S.next){var V=S.value;if(r.length>o.length)return;if(!(V instanceof b)){var I=1,$;if(ae){if($=v(re,z,o,te),!$||$.index>=o.length)break;var L=$.index,ce=$.index+$[0].length,T=z;for(T+=S.value.length;L>=T;)S=S.next,T+=S.value.length;if(T-=S.value.length,z=T,S.value instanceof b)continue;for(var R=S;R!==r.tail&&(T<ce||typeof R.value=="string");R=R.next)I++,T+=R.value.length;I--,V=o.slice(z,T),$.index-=z}else if($=v(re,0,V,te),!$)continue;var L=$.index,_=$[0],G=V.slice(0,L),oe=V.slice(L+_.length),Y=z+V.length;m&&Y>m.reach&&(m.reach=Y);var U=S.prev;G&&(U=C(r,U,G),z+=G.length),N(r,U,I);var ue=new b(y,q?s.tokenize(_,q):_,le,_);if(S=C(r,U,ue),oe&&C(r,S,oe),I>1){var W={cause:y+","+x,reach:Y};g(o,r,l,S.prev,z,W),m&&W.reach>m.reach&&(m.reach=W.reach)}}}}}}function F(){var o={value:null,prev:null,next:null},r={value:null,prev:o,next:null};o.next=r,this.head=o,this.tail=r,this.length=0}function C(o,r,l){var d=r.next,c={value:l,prev:r,next:d};return r.next=c,d.prev=c,o.length++,c}function N(o,r,l){for(var d=r.next,c=0;c<l&&d!==o.tail;c++)d=d.next;r.next=d,d.prev=r,o.length-=c}function B(o){for(var r=[],l=o.head.next;l!==o.tail;)r.push(l.value),l=l.next;return r}if(!i.document)return i.addEventListener&&(s.disableWorkerMessageHandler||i.addEventListener("message",function(o){var r=JSON.parse(o.data),l=r.language,d=r.code,c=r.immediateClose;i.postMessage(s.highlight(d,s.languages[l],l)),c&&i.close()},!1)),s;var k=s.util.currentScript();k&&(s.filename=k.src,k.hasAttribute("data-manual")&&(s.manual=!0));function w(){s.manual||s.highlightAll()}if(!s.manual){var E=document.readyState;E==="loading"||E==="interactive"&&k&&k.defer?document.addEventListener("DOMContentLoaded",w):window.requestAnimationFrame?window.requestAnimationFrame(w):window.setTimeout(w,16)}return s}(n);e.exports&&(e.exports=t),typeof ie<"u"&&(ie.Prism=t),t.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},{pattern:/^(\s*)["']|["']$/,lookbehind:!0}]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},t.languages.markup.tag.inside["attr-value"].inside.entity=t.languages.markup.entity,t.languages.markup.doctype.inside["internal-subset"].inside=t.languages.markup,t.hooks.add("wrap",function(i){i.type==="entity"&&(i.attributes.title=i.content.replace(/&amp;/,"&"))}),Object.defineProperty(t.languages.markup.tag,"addInlined",{value:function(u,h){var f={};f["language-"+h]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:t.languages[h]},f.cdata=/^<!\[CDATA\[|\]\]>$/i;var s={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:f}};s["language-"+h]={pattern:/[\s\S]+/,inside:t.languages[h]};var b={};b[u]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return u}),"i"),lookbehind:!0,greedy:!0,inside:s},t.languages.insertBefore("markup","cdata",b)}}),Object.defineProperty(t.languages.markup.tag,"addAttribute",{value:function(i,u){t.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+i+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[u,"language-"+u],inside:t.languages[u]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),t.languages.html=t.languages.markup,t.languages.mathml=t.languages.markup,t.languages.svg=t.languages.markup,t.languages.xml=t.languages.extend("markup",{}),t.languages.ssml=t.languages.xml,t.languages.atom=t.languages.xml,t.languages.rss=t.languages.xml,function(i){var u=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/;i.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:RegExp("@[\\w-](?:"+/[^;{\s"']|\s+(?!\s)/.source+"|"+u.source+")*?"+/(?:;|(?=\s*\{))/.source),inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+u.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+u.source+"$"),alias:"url"}}},selector:{pattern:RegExp(`(^|[{}\\s])[^{}\\s](?:[^{};"'\\s]|\\s+(?![\\s{])|`+u.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:u,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},i.languages.css.atrule.inside.rest=i.languages.css;var h=i.languages.markup;h&&(h.tag.addInlined("style","css"),h.tag.addAttribute("style","css"))}(t),t.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},t.languages.javascript=t.languages.extend("clike",{"class-name":[t.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+(/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|"+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source)+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),t.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,t.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:t.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:t.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:t.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:t.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:t.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),t.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:t.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),t.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),t.languages.markup&&(t.languages.markup.tag.addInlined("script","javascript"),t.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),t.languages.js=t.languages.javascript,function(){if(typeof t>"u"||typeof document>"u")return;Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector);var i="Loading\u2026",u=function(k,w){return"\u2716 Error "+k+" while fetching file: "+w},h="\u2716 Error: File does not exist or is empty",f={js:"javascript",py:"python",rb:"ruby",ps1:"powershell",psm1:"powershell",sh:"bash",bat:"batch",h:"c",tex:"latex"},s="data-src-status",b="loading",v="loaded",g="failed",F="pre[data-src]:not(["+s+'="'+v+'"]):not(['+s+'="'+b+'"])';function C(k,w,E){var o=new XMLHttpRequest;o.open("GET",k,!0),o.onreadystatechange=function(){o.readyState==4&&(o.status<400&&o.responseText?w(o.responseText):o.status>=400?E(u(o.status,o.statusText)):E(h))},o.send(null)}function N(k){var w=/^\s*(\d+)\s*(?:(,)\s*(?:(\d+)\s*)?)?$/.exec(k||"");if(w){var E=Number(w[1]),o=w[2],r=w[3];return o?r?[E,Number(r)]:[E,void 0]:[E,E]}}t.hooks.add("before-highlightall",function(k){k.selector+=", "+F}),t.hooks.add("before-sanity-check",function(k){var w=k.element;if(w.matches(F)){k.code="",w.setAttribute(s,b);var E=w.appendChild(document.createElement("CODE"));E.textContent=i;var o=w.getAttribute("data-src"),r=k.language;if(r==="none"){var l=(/\.(\w+)$/.exec(o)||[,"none"])[1];r=f[l]||l}t.util.setLanguage(E,r),t.util.setLanguage(w,r);var d=t.plugins.autoloader;d&&d.loadLanguages(r),C(o,function(c){w.setAttribute(s,v);var m=N(w.getAttribute("data-range"));if(m){var y=c.split(/\r\n?|\n/g),p=m[0],x=m[1]==null?y.length:m[1];p<0&&(p+=y.length),p=Math.max(0,Math.min(p-1,y.length)),x<0&&(x+=y.length),x=Math.max(0,Math.min(x,y.length)),c=y.slice(p,x).join(`
`),w.hasAttribute("data-start")||w.setAttribute("data-start",String(p+1))}E.textContent=c,t.highlightElement(E)},function(c){w.setAttribute(s,g),E.textContent=c})}}),t.plugins.fileHighlight={highlight:function(w){for(var E=(w||document).querySelectorAll(F),o=0,r;r=E[o++];)t.highlightElement(r)}};var B=!1;t.fileHighlight=function(){B||(console.warn("Prism.fileHighlight is deprecated. Use `Prism.plugins.fileHighlight.highlight` instead."),B=!0),t.plugins.fileHighlight.highlight.apply(this,arguments)}}()})(K);Prism.languages.json={property:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?=\s*:)/,lookbehind:!0,greedy:!0},string:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?!\s*:)/,lookbehind:!0,greedy:!0},comment:{pattern:/\/\/.*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},number:/-?\b\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,punctuation:/[{}[\],]/,operator:/:/,boolean:/\b(?:false|true)\b/,null:{pattern:/\bnull\b/,alias:"keyword"}};Prism.languages.webmanifest=Prism.languages.json;const P=(e,n="json")=>{const t=n==="json"?JSON.stringify(e,null,2):e;if(e){let u=K.exports.highlight(t,K.exports.languages[n],n).split(`
`).map((h,f)=>`<span class="line-number text-slate-700 select-none contents align-top">${(f+1).toString().padStart(4," ")}  </span>${h}`).join(`
`);return Q(t)&&(u=u.replaceAll('<span class="token punctuation">{</span>','<details class="contents" open><summary class="inline-block brace"><span class="token punctuation">{</span></summary>').replaceAll('<span class="token punctuation">[</span>','<details class="contents" open><summary class="inline-block bracket"><span class="token punctuation">[</span></summary>').replaceAll('<span class="token punctuation">}</span>','</details><span class="token punctuation inline-block">}</span>').replaceAll('<span class="token punctuation">]</span>','</details><span class="token punctuation inline-block">]</span>')),`<code class="language-${n}">${u}</code>`}return""},ve=e=>{try{return new DOMParser().parseFromString(e,"application/xml").documentElement.nodeName!=="parsererror"}catch{return!1}};function ke(e){try{return new DOMParser().parseFromString(e,"text/html").documentElement.nodeName==="html"}catch{return!1}}function Ee(e){return e instanceof Blob}function Ae(e){return Q(e)?"json":ve(e)?"xml":ke(e)?"html":Ee(e)?"blob":"plaintext"}const Fe=(e,n,t,i,u)=>{var m;const{doc:h,testId:f}=M(),s=Cypress.log({name:n.method||"GET",autoEnd:!1,message:`${n.url}`}).snapshot("request"),{body:b,status:v,headers:g,statusText:F,duration:C}=e,N=`${v}\xA0(${F})`;t[i].status=N||"",t[i].time=C;const B=g["content-type"],k=g["content-length"],w=g["set-cookie"],o=typeof b==="object"?JSON.stringify(b,null,2):b;if(B){const y=B.split(";")[0],A={"text/xml":"xml","application/json":"json","text/html":"html","text/plain":"plaintext"}[y]||Ae(b);t[i].responseBody.formatted=P(b,A),t[i].responseBody.body=o}const r=D.exports.parse(w,{decodeValues:!0});t[i].cookies.body=r,t[i].requestBody.formatted.length||(t[i].requestBody.formatted='<div class="pl-4 text-cy-gray text-xs font-mono">(No content)</div>'),t[i].responseBody.formatted.length||(t[i].responseBody.formatted='<div class="pl-4 text-cy-gray text-xs font-mono">(No content)</div>'),t[i].responseHeaders.body=g,t[i].responseHeaders.formatted=P(g);const l=k?parseInt(k):xe((m=t[i].responseBody.body)==null?void 0:m.value);t[i].size=we(l),e.size=l;const d=e,c=()=>Cypress.$(`#${t[i].id}`,{log:!1});cy.window({log:!1}).then(c).then(y=>{var p;return s.set({consoleProps(){return{yielded:d}}}),window.props[f]=t,s.set({$el:y}),s.snapshot("response").end(),(p=h.getElementById("api-view-bottom"))==null||p.scrollIntoView(),Cypress.env("snapshotOnly")&&(u.unmount(),ye()),e})},Se={class:"flex text-cy-gray rounded-sm bg-cy-blue-darkest py-1.5 border-slate-800 border mb-2"},Ce=["value"],qe=a.defineComponent({__name:"TitlePanel",props:{method:{default:"",type:String},url:{default:"",type:String}},setup(e){const n=t=>({DELETE:"text-cy-red",POST:"text-cy-green",PUT:"text-cy-green",GET:"text-cy-blue",PATCH:"text-cy-orange",HEAD:"text-cy-yellow"})[t];return(t,i)=>(a.openBlock(),a.createElementBlock("div",null,[a.createElementVNode("div",Se,[a.createElementVNode("p",{"data-cy":"method",class:a.normalizeClass(["mx-2 rounded-sm font-mono",n(e.method)])},a.toDisplayString(e.method),3),a.createElementVNode("input",{"data-cy":"url",class:"px-1 inline-block font-mono bg-cy-blue-darkest w-full outline-0",value:e.url,readonly:""},null,8,Ce)])]))}}),$e=["data-cy"],Be=["innerHTML"],O=a.defineComponent({__name:"CodeBlock",props:{selector:{type:String},dataFormatted:{default:"",type:String}},setup(e){return(n,t)=>e.dataFormatted?(a.openBlock(),a.createElementBlock("div",{key:0,class:"bg-cy-blue-darker","data-cy":e.selector},[a.createElementVNode("pre",{class:"hljs overflow-scroll no-scrollbar",innerHTML:e.dataFormatted},null,8,Be)],8,$e)):a.createCommentVNode("",!0)}}),ze={"data-cy":"requestPanel",class:"col-span-1"},Ne=["id","name","checked"],Te=["for"],Ve=["id","name","checked"],He=["for"],Pe=["id","name","checked"],Oe=["for"],Re=["id","name","checked"],je=["for"],Me=a.defineComponent({__name:"RequestPanel",props:{item:{type:Object},index:{type:[Number,String]}},setup(e){return(n,t)=>{var i,u,h,f,s,b,v,g,F,C,N,B,k,w,E,o,r,l,d,c,m,y,p,x,A,q;return a.openBlock(),a.createElementBlock("div",ze,[a.createVNode(qe,{method:(i=e.item)==null?void 0:i.method,url:(u=e.item)==null?void 0:u.url},null,8,["method","url"]),a.createElementVNode("input",{id:"query"+e.index,type:"radio",class:"hidden invisible",name:"req"+e.index,"data-cy":"showQuery",checked:((h=e.item)==null?void 0:h.query.body)&&!((f=e.item)!=null&&f.requestBody.body)},null,8,Ne),a.withDirectives(a.createElementVNode("label",{class:"pr-3 pl-1 cursor-pointer text-cy-gray select-none",for:"query"+e.index}," Query ",8,Te),[[a.vShow,(s=e.item)==null?void 0:s.query.body]]),a.createElementVNode("input",{id:"requestHeaders"+e.index,type:"radio",class:"hidden invisible",name:"req"+e.index,"data-cy":"showRequestHeaders",checked:((b=e.item)==null?void 0:b.requestHeaders.body)&&!((v=e.item)!=null&&v.requestBody.body)&&!((g=e.item)!=null&&g.query.body)&&!((F=e.item)!=null&&F.auth.body)},null,8,Ve),a.withDirectives(a.createElementVNode("label",{class:"pr-4 pl-1 cursor-pointer text-cy-gray select-none",for:"requestHeaders"+e.index}," Headers ",8,He),[[a.vShow,(C=e.item)==null?void 0:C.requestHeaders.body]]),a.createElementVNode("input",{id:"auth"+e.index,type:"radio",class:"hidden invisible",name:"req"+e.index,"data-cy":"showAuth",checked:((N=e.item)==null?void 0:N.auth.body)&&!((B=e.item)!=null&&B.requestBody.body)&&!((k=e.item)!=null&&k.query.body)},null,8,Pe),a.withDirectives(a.createElementVNode("label",{class:"pr-3 pl-1 cursor-pointer text-cy-gray select-none",for:"auth"+e.index}," Auth ",8,Oe),[[a.vShow,(w=e.item)==null?void 0:w.auth.body]]),a.createElementVNode("input",{id:"requestBody"+e.index,type:"radio",class:"hidden invisible",name:"req"+e.index,"data-cy":"showRequestBody",checked:((E=e.item)==null?void 0:E.requestBody.body)||!((o=e.item)!=null&&o.auth.body)&&!((r=e.item)!=null&&r.requestHeaders.body)&&!((l=e.item)!=null&&l.query.body)},null,8,Re),a.withDirectives(a.createElementVNode("label",{class:"pr-3 pl-1 cursor-pointer text-cy-gray select-none",for:"requestBody"+e.index}," Body ",8,je),[[a.vShow,((d=e.item)==null?void 0:d.requestBody.body)||!((c=e.item)!=null&&c.auth.body)&&!((m=e.item)!=null&&m.requestHeaders.body)&&!((y=e.item)!=null&&y.query.body)]]),a.createVNode(O,{"data-formatted":(p=e.item)==null?void 0:p.auth.formatted,selector:"auth"},null,8,["data-formatted"]),a.createVNode(O,{"data-formatted":(x=e.item)==null?void 0:x.query.formatted,selector:"query"},null,8,["data-formatted"]),a.createVNode(O,{"data-formatted":(A=e.item)==null?void 0:A.requestHeaders.formatted,selector:"requestHeaders"},null,8,["data-formatted"]),a.createVNode(O,{"data-formatted":(q=e.item)==null?void 0:q.requestBody.formatted,selector:"requestBody"},null,8,["data-formatted"])])}}}),De={class:"flex text-cy-gray py-1.5 mb-2"},Ie={"data-cy":"time",class:"mx-2 font-mono text-cy-green"},Le={key:0},_e={"data-cy":"size",class:"mx-2 font-mono text-cy-green"},Ue=a.defineComponent({__name:"StatusPanel",props:{status:{default:"",type:String},time:{default:0,type:Number},size:{default:"",type:String}},setup(e){const n=t=>{const i=t.substring(0,1);return{2:"text-cy-green",3:"text-cy-orange",4:"text-cy-red",5:"text-cy-red"}[i]};return(t,i)=>(a.openBlock(),a.createElementBlock("div",null,[a.createElementVNode("div",De,[a.createElementVNode("p",null,[a.createTextVNode(" Status:"),a.createElementVNode("span",{"data-cy":"status",class:a.normalizeClass(["mx-2 font-mono",n(e.status)])},a.toDisplayString(e.status),3)]),a.createElementVNode("p",null,[a.createTextVNode(" Duration:"),a.createElementVNode("span",Ie,a.toDisplayString(e.time)+"\xA0ms",1)]),e.size?(a.openBlock(),a.createElementBlock("p",Le,[a.createTextVNode(" Size:"),a.createElementVNode("span",_e,a.toDisplayString(e.size),1)])):a.createCommentVNode("",!0)])]))}}),Ge={key:0,class:"bg-cy-blue-darkest border-slate-800 px-2 py-2 border mt-2 overflow-auto","data-cy":"cookies"},Ye={class:"text-sm"},We=a.createElementVNode("thead",{class:"text-cy-gray-light w-2/12"},[a.createElementVNode("tr",null,[a.createElementVNode("th",{class:"w-24 text-left py-2 px-3 border-r border-b border-slate-800"}," Name "),a.createElementVNode("th",{class:"w-24 text-left py-2 px-3 border-l border-b border-slate-800"}," Value ")])],-1),Ze={class:"w-10/12 text-cy-gray"},Je={class:"font-mono py-2 px-3 text-left break-words border-r border-t border-slate-800"},Xe={class:"font-mono py-2 px-3 text-left break-words w-full border-l border-t border-slate-800"},Ke={key:1,class:"bg-cy-blue-darkest border-slate-800 p-4 border mt-2 overflow-auto text-cy-gray text-xs font-mono","data-cy":"cookies"},Qe=a.defineComponent({__name:"CookieTable",props:{data:{type:Object}},setup(e){return(n,t)=>{var i;return(i=e.data)!=null&&i.length?(a.openBlock(),a.createElementBlock("div",Ge,[a.createElementVNode("table",Ye,[We,a.createElementVNode("tbody",Ze,[(a.openBlock(!0),a.createElementBlock(a.Fragment,null,a.renderList(e.data,u=>(a.openBlock(),a.createElementBlock("tr",{key:u.name},[a.createElementVNode("td",Je,a.toDisplayString(u.name),1),a.createElementVNode("td",Xe,a.toDisplayString(u.value),1)]))),128))])])])):(a.openBlock(),a.createElementBlock("div",Ke," (No cookies were set) "))}}}),et={"data-cy":"responsePanel",class:"col-span-1"},tt=["id","name"],at=["for"],rt=["id","name"],ot=["for"],nt=["id","name"],it=["for"],st=a.defineComponent({__name:"ResponsePanel",props:{item:{type:Object},index:{type:[Number,String]}},setup(e){return(n,t)=>{var i,u,h,f,s,b,v,g,F,C;return a.openBlock(),a.createElementBlock("div",et,[a.createVNode(Ue,{status:(i=e.item)==null?void 0:i.status,time:(u=e.item)==null?void 0:u.time,size:(h=e.item)==null?void 0:h.size},null,8,["status","time","size"]),a.createElementVNode("input",{id:"responseBody"+e.index,type:"radio",class:"hidden invisible",name:"res"+e.index,"data-cy":"showResponseBody",checked:""},null,8,tt),a.withDirectives(a.createElementVNode("label",{class:"pr-4 md:pl-1 cursor-pointer text-cy-gray",for:"responseBody"+e.index}," Response ",8,at),[[a.vShow,(f=e.item)==null?void 0:f.responseBody.body]]),a.createElementVNode("input",{id:"responseHeaders"+e.index,type:"radio",class:"hidden invisible",name:"res"+e.index,"data-cy":"showResponseHeaders"},null,8,rt),a.withDirectives(a.createElementVNode("label",{class:"pr-4 pl-1 cursor-pointer text-cy-gray",for:"responseHeaders"+e.index}," Headers ",8,ot),[[a.vShow,(s=e.item)==null?void 0:s.responseHeaders.body]]),a.createElementVNode("input",{id:"cookies"+e.index,type:"radio",class:"hidden invisible",name:"res"+e.index,"data-cy":"showCookies"},null,8,nt),a.withDirectives(a.createElementVNode("label",{class:"pr-4 pl-1 cursor-pointer text-cy-gray",for:"cookies"+e.index}," Cookies ",8,it),[[a.vShow,(b=e.item)==null?void 0:b.cookies.body]]),a.createVNode(O,{"data-formatted":(v=e.item)==null?void 0:v.responseBody.formatted,show:(g=e.item)==null?void 0:g.responseBody.formatted.length,selector:"responseBody"},null,8,["data-formatted","show"]),a.createVNode(O,{"data-formatted":(F=e.item)==null?void 0:F.responseHeaders.formatted,selector:"responseHeaders"},null,8,["data-formatted"]),a.createVNode(Qe,{data:(C=e.item)==null?void 0:C.cookies.body},null,8,["data"])])}}}),lt={id:"api-view"},dt=["id"],ct={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ut=a.createElementVNode("div",{class:"separator"},"...",-1),pt=a.createElementVNode("hr",{class:"border-slate-800 mt-6 hidden md:block"},null,-1),mt=a.createElementVNode("div",{id:"api-view-bottom"},null,-1),gt=a.defineComponent({__name:"App",props:{props:{default:()=>[],type:Object}},setup(e){const n=e;return(t,i)=>(a.openBlock(),a.createElementBlock("div",lt,[(a.openBlock(!0),a.createElementBlock(a.Fragment,null,a.renderList(n.props,(u,h)=>(a.openBlock(),a.createElementBlock("div",{key:u.id},[a.createElementVNode("section",{id:u.id,class:"bg-cy-blue-darker rounded-sm m-4 p-4 pb-2"},[a.createElementVNode("div",ct,[a.createVNode(Me,{item:u,index:h},null,8,["item","index"]),a.createVNode(st,{item:u,index:h},null,8,["item","index"])]),ut,pt],8,dt)]))),128)),mt]))}}),ht=`*,:before,:after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}:before,:after{--tw-content: ""}html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji"}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-size:100%;font-weight:inherit;line-height:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,[type=button],[type=reset],[type=submit]{-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dl,dd,h1,h2,h3,h4,h5,h6,hr,figure,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}ol,ul,menu{list-style:none;margin:0;padding:0}textarea{resize:vertical}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color:#9ca3af}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}button,[role=button]{cursor:pointer}:disabled{cursor:default}img,svg,video,canvas,audio,iframe,embed,object{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}*,:before,:after{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: }::backdrop{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: }.invisible{visibility:hidden}.col-span-1{grid-column:span 1 / span 1}.m-4{margin:1rem}.mx-2{margin-left:.5rem;margin-right:.5rem}.mt-6{margin-top:1.5rem}.mt-2{margin-top:.5rem}.mb-2{margin-bottom:.5rem}.inline-block{display:inline-block}.flex{display:flex}.table{display:table}.grid{display:grid}.contents{display:contents}.hidden{display:none}.w-2\\/12{width:16.666667%}.w-24{width:6rem}.w-10\\/12{width:83.333333%}.w-full{width:100%}.transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.cursor-pointer{cursor:pointer}.select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.gap-4{gap:1rem}.overflow-auto{overflow:auto}.overflow-scroll{overflow:scroll}.break-words{overflow-wrap:break-word}.rounded-sm{border-radius:.125rem}.border{border-width:1px}.border-r{border-right-width:1px}.border-b{border-bottom-width:1px}.border-l{border-left-width:1px}.border-t{border-top-width:1px}.border-slate-800{--tw-border-opacity: 1;border-color:rgb(30 41 59 / var(--tw-border-opacity))}.bg-cy-blue-darker{--tw-bg-opacity: 1;background-color:rgb(27 30 46 / var(--tw-bg-opacity))}.bg-cy-blue-darkest{--tw-bg-opacity: 1;background-color:rgb(23 25 38 / var(--tw-bg-opacity))}.p-4{padding:1rem}.px-2{padding-left:.5rem;padding-right:.5rem}.py-2{padding-top:.5rem;padding-bottom:.5rem}.px-3{padding-left:.75rem;padding-right:.75rem}.py-1\\.5{padding-top:.375rem;padding-bottom:.375rem}.py-1{padding-top:.25rem;padding-bottom:.25rem}.px-1{padding-left:.25rem;padding-right:.25rem}.pb-2{padding-bottom:.5rem}.pr-3{padding-right:.75rem}.pl-1{padding-left:.25rem}.pr-4{padding-right:1rem}.pl-4{padding-left:1rem}.text-left{text-align:left}.align-top{vertical-align:top}.font-mono{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}.text-sm{font-size:.875rem;line-height:1.25rem}.text-xs{font-size:.75rem;line-height:1rem}.text-cy-gray-light{--tw-text-opacity: 1;color:rgb(208 210 224 / var(--tw-text-opacity))}.text-cy-gray{--tw-text-opacity: 1;color:rgb(144 149 173 / var(--tw-text-opacity))}.text-cy-green{--tw-text-opacity: 1;color:rgb(31 169 113 / var(--tw-text-opacity))}.text-cy-orange{--tw-text-opacity: 1;color:rgb(219 121 5 / var(--tw-text-opacity))}.text-cy-red{--tw-text-opacity: 1;color:rgb(255 87 112 / var(--tw-text-opacity))}.text-cy-blue{--tw-text-opacity: 1;color:rgb(100 112 243 / var(--tw-text-opacity))}.text-cy-yellow{--tw-text-opacity: 1;color:rgb(237 187 74 / var(--tw-text-opacity))}.text-slate-700{--tw-text-opacity: 1;color:rgb(51 65 85 / var(--tw-text-opacity))}.outline-0{outline-width:0px}#api-view{position:fixed;top:0px;left:0px;right:0px;bottom:0px;margin-bottom:2.5rem;height:100vh;overflow:scroll;--tw-bg-opacity: 1;background-color:rgb(27 30 46 / var(--tw-bg-opacity));z-index:9999999}@keyframes fade-out{0%{opacity:1}to{opacity:0}}.__cypress-highlight{opacity:.2!important;pointer-events:none!important}[data-layer=Padding],[data-layer=Border],[data-layer=Content]{opacity:0!important}[data-layer=Margin]{--tw-bg-opacity: 1 !important;background-color:rgb(254 249 195 / var(--tw-bg-opacity))!important;opacity:0;animation:fade-out 2s ease-in-out}[data-cy=showAuth]:checked+label,[data-cy=showQuery]:checked+label,[data-cy=showRequestHeaders]:checked+label,[data-cy=showRequestBody]:checked+label,[data-cy=showResponseBody]:checked+label,[data-cy=showResponseHeaders]:checked+label,[data-cy=showCookies]:checked+label{--tw-text-opacity: 1;color:rgb(208 210 224 / var(--tw-text-opacity))}[data-cy=showAuth]:checked~[data-cy=auth],[data-cy=showQuery]:checked~[data-cy=query],[data-cy=showRequestHeaders]:checked~[data-cy=requestHeaders],[data-cy=showRequestBody]:checked~[data-cy=requestBody],[data-cy=showResponseBody]:checked~[data-cy=responseBody],[data-cy=showResponseHeaders]:checked~[data-cy=responseHeaders],[data-cy=showCookies]:checked~[data-cy=cookies]{display:block}[data-cy=showAuth]:not(:checked)~[data-cy=auth],[data-cy=showQuery]:not(:checked)~[data-cy=query],[data-cy=showRequestHeaders]:not(:checked)~[data-cy=requestHeaders],[data-cy=showRequestBody]:not(:checked)~[data-cy=requestBody],[data-cy=showResponseBody]:not(:checked)~[data-cy=responseBody],[data-cy=showResponseHeaders]:not(:checked)~[data-cy=responseHeaders],[data-cy=showCookies]:not(:checked)~[data-cy=cookies]{display:none}pre{padding:.75rem;font-size:.75rem;line-height:1rem;--tw-text-opacity: 1;color:rgb(208 210 224 / var(--tw-text-opacity))}pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{margin-top:.5rem;border-width:1px;--tw-border-opacity: 1;border-color:rgb(30 41 59 / var(--tw-border-opacity));--tw-bg-opacity: 1;background-color:rgb(23 25 38 / var(--tw-bg-opacity));padding-top:1rem;padding-bottom:1rem;padding-left:0;padding-right:1rem}.hljs ::-moz-selection,.hljs::-moz-selection{--tw-bg-opacity: 1;background-color:rgb(46 50 71 / var(--tw-bg-opacity))}.hljs ::selection,.hljs::selection{--tw-bg-opacity: 1;background-color:rgb(46 50 71 / var(--tw-bg-opacity))}.comment{--tw-text-opacity: 1;color:rgb(144 149 173 / var(--tw-text-opacity))}.tag{--tw-text-opacity: 1;color:rgb(100 112 243 / var(--tw-text-opacity))}.operator,.punctuation,.subst{--tw-text-opacity: 1;color:rgb(144 149 173 / var(--tw-text-opacity))}.operator{opacity:.7}.bullet,.deletion,.name,.selector-tag,.template-variable,.variable{--tw-text-opacity: 1;color:rgb(255 87 112 / var(--tw-text-opacity))}.attr,.link,.literal,.number,.symbol,.variable.constant_{--tw-text-opacity: 1;color:rgb(208 210 224 / var(--tw-text-opacity))}.number,.attr-name{--tw-text-opacity: 1;color:rgb(31 169 113 / var(--tw-text-opacity))}.attr-value{--tw-text-opacity: 1;color:rgb(208 210 224 / var(--tw-text-opacity))}.addition,.built_in,.code,.doctag,.keyword.atrule,.quote,.regexp,.string,.title.class_.inherited__{--tw-text-opacity: 1;color:rgb(100 112 243 / var(--tw-text-opacity))}.diff .meta,.keyword,.template-tag,.type{--tw-text-opacity: 1;color:rgb(127 67 201 / var(--tw-text-opacity))}.meta,.meta .keyword,.meta .string{--tw-text-opacity: 1;color:rgb(144 149 173 / var(--tw-text-opacity))}.no-scrollbar::-webkit-scrollbar{display:none}.no-scrollbar{scrollbar-width:none}.line-number{display:inline-block;padding-right:.5rem}details summary{cursor:pointer;--tw-text-opacity: 1;color:rgb(144 149 173 / var(--tw-text-opacity))}code>details:first-of-type>summary{margin-left:-1rem}details[open] summary.brace:before{content:" \\25bc  ";margin-left:-.5rem;--tw-text-opacity: 1;color:rgb(51 65 85 / var(--tw-text-opacity))}details[open] summary.bracket:before{content:" \\25bc  ";margin-left:-.5rem;--tw-text-opacity: 1;color:rgb(51 65 85 / var(--tw-text-opacity))}details:not([open]) summary.brace:before{content:" \\25b6\\fe0e  ";margin-left:-.5rem;--tw-text-opacity: 1;color:rgb(51 65 85 / var(--tw-text-opacity))}details:not([open]) summary.bracket:before{content:" \\25b6\\fe0e  ";margin-left:-.5rem;--tw-text-opacity: 1;color:rgb(51 65 85 / var(--tw-text-opacity))}details:not([open]) summary.brace:after{content:"\\2026";display:inline-block;width:100%}details:not([open]) summary.bracket:after{content:"\\2026";display:inline-block;width:100%}details:not([open]){display:inline-block}.separator{font-size:50px;text-align:center;letter-spacing:5px;margin-top:-1.5rem;margin-bottom:-1.5rem;display:block;--tw-text-opacity: 1;color:rgb(46 50 71 / var(--tw-text-opacity))}@media (min-width: 768px){.separator{display:none}.md\\:block{display:block}.md\\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.md\\:pl-1{padding-left:.25rem}}
`,yt=`*,:before,:after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}:before,:after{--tw-content: ""}html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji"}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-size:100%;font-weight:inherit;line-height:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,[type=button],[type=reset],[type=submit]{-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dl,dd,h1,h2,h3,h4,h5,h6,hr,figure,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}ol,ul,menu{list-style:none;margin:0;padding:0}textarea{resize:vertical}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color:#9ca3af}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}button,[role=button]{cursor:pointer}:disabled{cursor:default}img,svg,video,canvas,audio,iframe,embed,object{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}*,:before,:after{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: }::backdrop{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: }.invisible{visibility:hidden}.col-span-1{grid-column:span 1 / span 1}.m-4{margin:1rem}.mx-2{margin-left:.5rem;margin-right:.5rem}.mt-6{margin-top:1.5rem}.mt-2{margin-top:.5rem}.mb-2{margin-bottom:.5rem}.inline-block{display:inline-block}.flex{display:flex}.table{display:table}.grid{display:grid}.contents{display:contents}.hidden{display:none}.w-2\\/12{width:16.666667%}.w-24{width:6rem}.w-10\\/12{width:83.333333%}.w-full{width:100%}.transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.cursor-pointer{cursor:pointer}.select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.gap-4{gap:1rem}.overflow-auto{overflow:auto}.overflow-scroll{overflow:scroll}.break-words{overflow-wrap:break-word}.rounded-sm{border-radius:.125rem}.border{border-width:1px}.border-r{border-right-width:1px}.border-b{border-bottom-width:1px}.border-l{border-left-width:1px}.border-t{border-top-width:1px}.border-slate-800{--tw-border-opacity: 1;border-color:rgb(30 41 59 / var(--tw-border-opacity))}.bg-cy-blue-darker{--tw-bg-opacity: 1;background-color:rgb(27 30 46 / var(--tw-bg-opacity))}.bg-cy-blue-darkest{--tw-bg-opacity: 1;background-color:rgb(23 25 38 / var(--tw-bg-opacity))}.p-4{padding:1rem}.px-2{padding-left:.5rem;padding-right:.5rem}.py-2{padding-top:.5rem;padding-bottom:.5rem}.px-3{padding-left:.75rem;padding-right:.75rem}.py-1\\.5{padding-top:.375rem;padding-bottom:.375rem}.py-1{padding-top:.25rem;padding-bottom:.25rem}.px-1{padding-left:.25rem;padding-right:.25rem}.pb-2{padding-bottom:.5rem}.pr-3{padding-right:.75rem}.pl-1{padding-left:.25rem}.pr-4{padding-right:1rem}.pl-4{padding-left:1rem}.text-left{text-align:left}.align-top{vertical-align:top}.font-mono{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}.text-sm{font-size:.875rem;line-height:1.25rem}.text-xs{font-size:.75rem;line-height:1rem}.text-cy-gray-light{--tw-text-opacity: 1;color:rgb(208 210 224 / var(--tw-text-opacity))}.text-cy-gray{--tw-text-opacity: 1;color:rgb(144 149 173 / var(--tw-text-opacity))}.text-cy-green{--tw-text-opacity: 1;color:rgb(31 169 113 / var(--tw-text-opacity))}.text-cy-orange{--tw-text-opacity: 1;color:rgb(219 121 5 / var(--tw-text-opacity))}.text-cy-red{--tw-text-opacity: 1;color:rgb(255 87 112 / var(--tw-text-opacity))}.text-cy-blue{--tw-text-opacity: 1;color:rgb(100 112 243 / var(--tw-text-opacity))}.text-cy-yellow{--tw-text-opacity: 1;color:rgb(237 187 74 / var(--tw-text-opacity))}.text-slate-700{--tw-text-opacity: 1;color:rgb(51 65 85 / var(--tw-text-opacity))}.outline-0{outline-width:0px}.command.command-name-GET span.command-method{border-radius:.125rem;--tw-bg-opacity: 1;background-color:rgb(100 112 243 / var(--tw-bg-opacity));padding-left:.375rem;padding-right:.375rem;color:#fff!important;min-width:10px;margin-right:6px}.command.command-name-DELETE span.command-method{border-radius:.125rem;--tw-bg-opacity: 1;background-color:rgb(255 87 112 / var(--tw-bg-opacity));padding-left:.375rem;padding-right:.375rem;color:#fff!important;min-width:10px;margin-right:6px}.command.command-name-PATCH span.command-method{border-radius:.125rem;--tw-bg-opacity: 1;background-color:rgb(219 121 5 / var(--tw-bg-opacity));padding-left:.375rem;padding-right:.375rem;color:#fff!important;min-width:10px;margin-right:6px}.command.command-name-HEAD span.command-method{border-radius:.125rem;--tw-bg-opacity: 1;background-color:rgb(237 187 74 / var(--tw-bg-opacity));padding-left:.375rem;padding-right:.375rem;color:#fff!important;min-width:10px;margin-right:6px}.command.command-name-PUT span.command-method,.command.command-name-POST span.command-method{border-radius:.125rem;--tw-bg-opacity: 1;background-color:rgb(31 169 113 / var(--tw-bg-opacity));padding-left:.375rem;padding-right:.375rem;color:#fff!important;min-width:10px;margin-right:6px}.command.command-name-GET span.command-method:before,.command.command-name-DELETE span.command-method:before,.command.command-name-PATCH span.command-method:before,.command.command-name-HEAD span.command-method:before,.command.command-name-PUT span.command-method:before,.command.command-name-POST span.command-method:before{content:""}@media (min-width: 768px){.md\\:block{display:block}.md\\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.md\\:pl-1{padding-left:.25rem}}
`,bt=()=>{const{doc:e}=M(),n=e.head||e.getElementsByTagName("head")[0],t=e.createElement("style");t.setAttribute("id","api-plugin-styles"),n.appendChild(t),t.appendChild(e.createTextNode(ht));const i=(top==null?void 0:top.document.querySelector("#unified-reporter"))||(top==null?void 0:top.document.querySelector("#app")),u=document.createElement("style");i==null||i.appendChild(u),u.appendChild(e.createTextNode(yt))},ft=e=>{const{doc:n}=M();bt();const t=n.createElement("div");t.setAttribute("id","api-plugin-root"),n.body.appendChild(t);const i=n.getElementById("api-plugin-root");e.mount(i)},{_:wt}=Cypress,xt=()=>{var g;const e={id:wt.uniqueId(),method:"GET",status:"",time:0,size:"",url:"",auth:{body:{},formatted:""},query:{body:{},formatted:""},requestHeaders:{body:{},formatted:""},requestBody:{body:{},formatted:""},responseBody:{body:{},formatted:""},responseHeaders:{body:{},formatted:""},cookies:{body:{}}},{doc:n,attempt:t,testId:i}=M(),u=t!==0,h=n.URL!=="about:blank",f=!!((g=window.props[i])!=null&&g.length),s=f&&!u?window.props[i]:[];s.push(e);const b=a.reactive(s),v=a.createApp(gt,{props:b});return(!f||u||Cypress.env("snapshotOnly")||h)&&ft(v),{app:v,props:b}},vt=(e,n)=>{e[n].requestBody.formatted=P(e[n].requestBody.body),e[n].requestHeaders.formatted=P(e[n].requestHeaders.body),e[n].query.formatted=P(e[n].query.body),e[n].auth.formatted=P(e[n].auth.body)},kt=e=>{try{return new URL(e)}catch{return null}},Et=e=>/(\b(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b)/g.test(e),At=e=>kt(e)||Et(e),{_:j}=Cypress,Ft=(e,n,t)=>{e[n].method=j.cloneDeep(t.method)||"GET",e[n].url=At(t.url)?t.url:Cypress.config("baseUrl")+t.url,e[n].query.body=j.cloneDeep(t.qs),e[n].auth.body=j.cloneDeep(t.auth),e[n].requestHeaders.body=j.cloneDeep(t.headers),e[n].requestBody.body=j.cloneDeep(t.body)},St=cy.request.bind({}),ee=(...e)=>{const{props:n,app:t}=xt(),i=ge(...e),u=n.length-1;return Ft(n,u,i),Cypress.env("hideCredentials")&&(n[u]=he(n[u])),vt(n,u),St({...i,log:!1}).then(h=>Fe(h,i,n,u,t))};before(()=>{window.props={}});Cypress.Commands.addAll({api:ee});Cypress.Commands.overwrite("request",(e,...n)=>Cypress.env("requestMode")?ee(...n):e(...n));const Ct=Object.freeze(Object.defineProperty({__proto__:null,api:ee},Symbol.toStringTag,{value:"Module"}));module.exports=Ct;
//# sourceMappingURL=support.js.map
