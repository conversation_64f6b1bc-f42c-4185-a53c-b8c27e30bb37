{"author": {"name": "<PERSON><PERSON>", "url": "http://2dubs.com"}, "bugs": {"url": "https://github.com/velocityzen/fsu/issues"}, "description": "Unique file name with streams support", "homepage": "https://github.com/velocityzen/fsu#readme", "keywords": ["counter", "file", "filename", "fs", "stream", "unique"], "license": "MIT", "main": "./index", "name": "fsu", "repository": {"type": "git", "url": "git+ssh://**************/velocityzen/fsu.git"}, "version": "1.1.1", "devDependencies": {"ava": "^0.25.0", "del": "^3.0.0", "nyc": "^11.7.1"}, "scripts": {"test": "nyc ava"}}