/**
 * Git and CI/CD Setup Module
 *
 * This module handles Git repository initialization and GitHub Actions
 * CI/CD workflow setup. It provides functionality to:
 * - Initialize a new Git repository
 * - Create appropriate .gitignore file for Cypress projects
 * - Set up GitHub Actions workflow for automated testing
 *
 * The module ensures proper version control setup and continuous
 * integration capabilities for the Cypress testing project.
 */

const execa = require('execa');     // Better child process execution
const fs = require('fs-extra');    // Enhanced file system operations
const path = require('path');      // Node.js path manipulation utilities

/**
 * Initializes a Git repository and creates a Cypress-specific .gitignore file
 *
 * This function sets up version control for the project by:
 * 1. Initializing a new Git repository
 * 2. Creating a .gitignore file with Cypress-specific exclusions
 *
 * The .gitignore file excludes:
 * - node_modules/ (NPM dependencies)
 * - cypress/videos/ (test execution videos)
 * - cypress/screenshots/ (test failure screenshots)
 * - cypress/reports/ (test execution reports)
 * - .DS_Store (macOS system files)
 * - *.log (log files)
 *
 * @returns {Promise<boolean>} Success status of Git initialization
 */
exports.initGit = async function() {
  try {
    // Initialize Git repository in current directory
    await execa('git', ['init']);

    // Create comprehensive .gitignore file for Cypress projects
    const gitignoreContent = `
# Dependencies
node_modules/

# Cypress artifacts
cypress/videos/
cypress/screenshots/
cypress/reports/

# System files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
`;

    // Write .gitignore file to project root
    await fs.writeFile('.gitignore', gitignoreContent.trim());

    return true;  // Git initialization successful
  } catch (error) {
    console.error('Failed to initialize Git:', error.message);
    return false;  // Git initialization failed
  }
};

/**
 * Sets up GitHub Actions CI/CD workflow for automated Cypress testing
 *
 * This function creates a comprehensive GitHub Actions workflow that:
 * 1. Triggers on pushes and pull requests to main/master branches
 * 2. Sets up Node.js environment
 * 3. Installs project dependencies
 * 4. Runs Cypress tests in headless mode
 * 5. Uploads test artifacts (screenshots, videos) on failure
 *
 * The workflow is designed to provide immediate feedback on code changes
 * and maintain code quality through automated testing.
 *
 * @returns {Promise<boolean>} Success status of GitHub Actions setup
 */
exports.setupGitHubActions = async function() {
  try {
    // Ensure .github/workflows directory exists
    const workflowDir = path.join(process.cwd(), '.github/workflows');
    await fs.ensureDir(workflowDir);

    // Define comprehensive GitHub Actions workflow
    const workflowContent = `
name: Cypress Tests

# Trigger workflow on pushes and pull requests to main branches
on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  cypress-run:
    runs-on: ubuntu-latest

    steps:
      # Checkout the repository code
      - name: Checkout
        uses: actions/checkout@v3

      # Setup Node.js environment
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'

      # Install project dependencies
      - name: Install dependencies
        run: npm ci

      # Run Cypress tests
      - name: Cypress run
        uses: cypress-io/github-action@v5
        with:
          browser: chrome
          headless: true

      # Upload screenshots on test failure
      - name: Upload screenshots
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: cypress-screenshots
          path: cypress/screenshots
          retention-days: 7

      # Upload videos for all test runs
      - name: Upload videos
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: cypress-videos
          path: cypress/videos
          retention-days: 7

      # Upload test reports
      - name: Upload test reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: cypress-reports
          path: cypress/reports
          retention-days: 30
`;

    // Write workflow file to .github/workflows directory
    await fs.writeFile(path.join(workflowDir, 'cypress.yml'), workflowContent.trim());

    return true;  // GitHub Actions setup successful
  } catch (error) {
    console.error('Failed to setup GitHub Actions:', error.message);
    return false;  // GitHub Actions setup failed
  }
};