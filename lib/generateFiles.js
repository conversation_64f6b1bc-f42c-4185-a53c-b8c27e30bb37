/**
 * Boilerplate File Generation Module
 *
 * This module is responsible for generating all the necessary boilerplate files
 * for a Cypress testing project. It creates configuration files, test examples,
 * page objects, fixtures, and support files based on user preferences.
 *
 * Generated files include:
 * - Cypress configuration file (cypress.config.js/ts)
 * - Support files (commands, e2e setup)
 * - Sample test files (UI and API examples)
 * - Page Object Model examples
 * - Test data fixtures
 * - TypeScript configuration (if TypeScript is enabled)
 * - Swagger/OpenAPI generated tests (if requested)
 */

const fs = require('fs-extra');  // Enhanced file system operations

/**
 * Generates all boilerplate files for the Cypress project
 *
 * This function creates a complete set of files needed for a functional
 * Cypress testing setup, including configuration, examples, and utilities.
 * The files are customized based on user preferences collected during setup.
 *
 * @param {Object} userChoices - User configuration preferences
 * @param {string} userChoices.baseUrl - Application base URL
 * @param {boolean} userChoices.useTypeScript - Whether to use TypeScript
 * @param {string} userChoices.browser - Preferred browser for testing
 * @param {boolean} userChoices.generateSwaggerTests - Whether to generate API tests
 * @param {string} userChoices.swaggerUrl - Swagger/OpenAPI JSON URL
 * @returns {Promise<Array<string>>} Array of generated file paths
 */
exports.generateBoilerplateFiles = async function(userChoices) {
  const { baseUrl, useTypeScript, browser, generateSwaggerTests, swaggerUrl } = userChoices;
  const ext = useTypeScript ? 'ts' : 'js';
  const generated = [];

  // Helper function to replace file extension placeholder
  const file = (filePath) => filePath.replace('.ext', `.${ext}`);

  // Generate Cypress configuration file
  const configFile = useTypeScript ? 'cypress.config.ts' : 'cypress.config.js';
  await fs.outputFile(configFile, `/**
 * Cypress Configuration File
 * Generated by Cypress Bootstrapper AI
 */

const { defineConfig } = require('cypress');

module.exports = defineConfig({
  e2e: {
    // Base URL for the application under test
    baseUrl: '${baseUrl || 'http://localhost:3000'}',

    // Default browser for test execution
    browser: '${browser || 'chrome'}',

    // Viewport settings
    viewportWidth: 1920,
    viewportHeight: 1080,

    // Timeout settings
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,

    // Node events configuration
    setupNodeEvents(on, config) {
      // Configure mochawesome reporter
      require('cypress-mochawesome-reporter/plugin')(on);

      // Browser launch configuration
      on('before:browser:launch', (browser = {}, launchOptions) => {
        if (browser.name === 'chrome' && browser.isHeadless) {
          launchOptions.args.push('--window-size=1920,1080');
        }
        return launchOptions;
      });

      return config;
    },

    // File patterns and locations
    specPattern: 'cypress/e2e/**/*.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/e2e.${ext}',
    fixturesFolder: 'cypress/fixtures',
    screenshotsFolder: 'cypress/screenshots',
    videosFolder: 'cypress/videos'
  },

  // Reporter configuration
  reporter: 'cypress-mochawesome-reporter',
  reporterOptions: {
    reportDir: 'cypress/reports',
    overwrite: false,
    html: true,
    json: true
  }
});
`);
  generated.push(configFile);

  // Generate support files (commands and e2e setup)
  await fs.outputFile(file('cypress/support/commands.ext'), `/**
 * Cypress Custom Commands
 * Generated by Cypress Bootstrapper AI
 */

/**
 * Custom command for user login
 * Usage: cy.login('username', 'password')
 */
Cypress.Commands.add('login', (username, password) => {
  cy.visit('/login');
  cy.get('#username').type(username);
  cy.get('#password').type(password);
  cy.get('button[type="submit"]').click();
});

/**
 * Custom command for authenticated API requests
 * Usage: cy.authenticatedRequest({ method: 'GET', url: '/api/users' })
 */
Cypress.Commands.add('authenticatedRequest', (options) => {
  const token = Cypress.env('authToken') || 'default-token';
  return cy.api({
    ...options,
    headers: {
      ...options.headers,
      'Authorization': \`Bearer \${token}\`
    }
  });
});

/**
 * Custom command for waiting for page load
 * Usage: cy.waitForPageLoad()
 */
Cypress.Commands.add('waitForPageLoad', () => {
  cy.get('[data-testid="loading"]', { timeout: 10000 }).should('not.exist');
  cy.get('main, [role="main"], #main-content', { timeout: 10000 }).should('be.visible');
});
`);
  generated.push(file('cypress/support/commands.ext'));

  await fs.outputFile(file('cypress/support/e2e.ext'), `/**
 * Cypress E2E Support Configuration
 * Generated by Cypress Bootstrapper AI
 */

// Import API testing plugin
import 'cypress-plugin-api';

// Import XPath support
import 'cypress-xpath';

// Import custom commands
import './commands.${ext}';

// Import test reporting
import 'cypress-mochawesome-reporter/register';

// Import and configure JSON schema validation
import chaiJsonSchema from 'chai-json-schema';
chai.use(chaiJsonSchema);
`);
  generated.push(file('cypress/support/e2e.ext'));

  // Generate sample test files for demonstration and learning
  await fs.outputFile(file('cypress/e2e/ui/tests/sample.spec.ext'), `/**
 * Sample UI Test
 * Generated by Cypress Bootstrapper AI
 *
 * This file demonstrates basic UI testing patterns and serves as
 * a starting point for writing your own UI tests.
 */

describe('Sample UI Test', () => {
  beforeEach(() => {
    // Visit the application before each test
    cy.visit('/');
  });

  it('should have the correct title', () => {
    // Test page title
    cy.title().should('include', 'Home');
  });

  it('should navigate to another page', () => {
    // Test navigation functionality
    cy.get('a[href="/about"]').click();
    cy.url().should('include', '/about');
  });

  it('should display main content', () => {
    // Test that main content is visible
    cy.get('main, [role="main"], #main-content').should('be.visible');
  });
});
`);
  generated.push(file('cypress/e2e/ui/tests/sample.spec.ext'));

  await fs.outputFile(file('cypress/e2e/api/sample-api.spec.ext'), `/**
 * Sample API Test
 * Generated by Cypress Bootstrapper AI
 *
 * This file demonstrates API testing patterns using the cy.api() command
 * and serves as a starting point for writing your own API tests.
 */

describe('Sample API Test', () => {
  it('should make a GET request', () => {
    cy.api({
      method: 'GET',
      url: 'https://jsonplaceholder.typicode.com/posts/1',
    }).then((response) => {
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('id', 1);
      expect(response.body).to.have.property('title');
      expect(response.body).to.have.property('body');
    });
  });

  it('should make a POST request', () => {
    const requestBody = {
      title: 'Test Post',
      body: 'This is a test post created by Cypress',
      userId: 1
    };

    cy.api({
      method: 'POST',
      url: 'https://jsonplaceholder.typicode.com/posts',
      body: requestBody
    }).then((response) => {
      expect(response.status).to.eq(201);
      expect(response.body).to.have.property('id');
      expect(response.body.title).to.eq(requestBody.title);
      expect(response.body.body).to.eq(requestBody.body);
    });
  });

  it('should handle API errors gracefully', () => {
    cy.api({
      method: 'GET',
      url: 'https://jsonplaceholder.typicode.com/posts/999999',
      failOnStatusCode: false
    }).then((response) => {
      expect(response.status).to.eq(404);
    });
  });
});
`);
  generated.push(file('cypress/e2e/api/sample-api.spec.ext'));

  // Generate Page Object Model example
  await fs.outputFile(file('cypress/e2e/ui/pages/HomePage.ext'), `/**
 * Home Page Object Model
 * Generated by Cypress Bootstrapper AI
 *
 * This class demonstrates the Page Object Model pattern for organizing
 * UI test code. It encapsulates page-specific functionality and selectors.
 */

class HomePage {
  /**
   * Navigate to the home page
   */
  visit() {
    cy.visit('/');
    return this;
  }

  /**
   * Get the page title
   * @returns {Cypress.Chainable} Page title
   */
  getTitle() {
    return cy.title();
  }

  /**
   * Get the navigation element
   * @returns {Cypress.Chainable} Navigation element
   */
  getNavigation() {
    return cy.get('nav');
  }

  /**
   * Click the About link in navigation
   */
  clickAboutLink() {
    cy.get('a[href="/about"]').click();
    return this;
  }

  /**
   * Verify the page is loaded
   */
  verifyPageLoaded() {
    cy.get('main, [role="main"], #main-content').should('be.visible');
    return this;
  }
}

export default new HomePage();
`);
  generated.push(file('cypress/e2e/ui/pages/HomePage.ext'));

  // Generate test data fixtures
  await fs.outputFile('cypress/fixtures/users.json', JSON.stringify([
    {
      "id": 1,
      "name": "Test User",
      "email": "<EMAIL>",
      "role": "admin",
      "active": true,
      "createdAt": "2024-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "name": "Regular User",
      "email": "<EMAIL>",
      "role": "user",
      "active": true,
      "createdAt": "2024-01-02T00:00:00Z"
    },
    {
      "id": 3,
      "name": "Inactive User",
      "email": "<EMAIL>",
      "role": "user",
      "active": false,
      "createdAt": "2024-01-03T00:00:00Z"
    }
  ], null, 2));
  generated.push('cypress/fixtures/users.json');

  // Generate API schema file for schema validation
  const schemaFile = useTypeScript ? 'cypress/fixtures/api-schemas.ts' : 'cypress/fixtures/api-schemas.js';
  const schemaContent = useTypeScript ? `/**
 * API Response Schema Definitions (TypeScript)
 * Generated by Cypress Bootstrapper AI
 */

export interface JsonSchema {
  type: string;
  properties?: Record<string, any>;
  required?: string[];
  items?: JsonSchema;
  enum?: string[];
  additionalProperties?: boolean | JsonSchema;
  format?: string;
}

export interface ApiSchemas {
  [key: string]: {
    [statusCode: number]: JsonSchema;
  };
}

// Common schema components
export const commonSchemas = {
  errorResponse: {
    type: 'object',
    properties: {
      code: { type: 'integer' },
      type: { type: 'string' },
      message: { type: 'string' }
    },
    required: ['code', 'type', 'message']
  } as JsonSchema
};

// API endpoint schemas
export const apiSchemas: ApiSchemas = {};

export function getSchema(method: string, path: string, statusCode: number = 200): JsonSchema | null {
  const key = \`\${method.toUpperCase()}:\${path}\`;
  const endpointSchemas = apiSchemas[key];

  if (!endpointSchemas) {
    console.warn(\`No schema found for \${key}\`);
    return null;
  }

  const schema = endpointSchemas[statusCode];
  if (!schema) {
    console.warn(\`No schema found for \${key} with status \${statusCode}\`);
    return null;
  }

  return schema;
}

export function validateResponseSchema(response: any, method: string, path: string): boolean {
  const schema = getSchema(method, path, response.status);

  if (!schema) {
    cy.log(\`⚠️ No schema available for \${method} \${path} (\${response.status})\`);
    return false;
  }

  try {
    expect(response.body).to.be.jsonSchema(schema);
    cy.log(\`✅ Schema validation passed for \${method} \${path} (\${response.status})\`);
    return true;
  } catch (error) {
    cy.log(\`❌ Schema validation failed for \${method} \${path} (\${response.status}): \${error.message}\`);
    throw error;
  }
}
` : `/**
 * API Response Schema Definitions (JavaScript)
 * Generated by Cypress Bootstrapper AI
 */

// Common schema components
const commonSchemas = {
  errorResponse: {
    type: 'object',
    properties: {
      code: { type: 'integer' },
      type: { type: 'string' },
      message: { type: 'string' }
    },
    required: ['code', 'type', 'message']
  }
};

// API endpoint schemas
const apiSchemas = {};

export function getSchema(method, path, statusCode = 200) {
  const key = \`\${method.toUpperCase()}:\${path}\`;
  const endpointSchemas = apiSchemas[key];

  if (!endpointSchemas) {
    console.warn(\`No schema found for \${key}\`);
    return null;
  }

  const schema = endpointSchemas[statusCode];
  if (!schema) {
    console.warn(\`No schema found for \${key} with status \${statusCode}\`);
    return null;
  }

  return schema;
}

export function validateResponseSchema(response, method, path) {
  const schema = getSchema(method, path, response.status);

  if (!schema) {
    cy.log(\`⚠️ No schema available for \${method} \${path} (\${response.status})\`);
    return false;
  }

  try {
    expect(response.body).to.be.jsonSchema(schema);
    cy.log(\`✅ Schema validation passed for \${method} \${path} (\${response.status})\`);
    return true;
  } catch (error) {
    cy.log(\`❌ Schema validation failed for \${method} \${path} (\${response.status}): \${error.message}\`);
    throw error;
  }
}

export { commonSchemas, apiSchemas };
`;

  await fs.outputFile(schemaFile, schemaContent);
  generated.push(schemaFile);

  // Generate Swagger/OpenAPI tests if requested by user
  if (generateSwaggerTests && swaggerUrl) {
    try {
      const { generateSwaggerApiTests } = require('./swaggerGenerator');
      const swaggerResult = await generateSwaggerApiTests(swaggerUrl, useTypeScript);

      // Add all generated Swagger test files to the tracking list
      generated.push(...swaggerResult.filePaths);

      // Log success information
      console.log(`✅ Generated ${swaggerResult.testFileCount} test files for ${swaggerResult.endpointCount} API endpoints from ${swaggerResult.pathCount} paths`);
      console.log(`📁 Swagger tests directory: ${swaggerResult.swaggerTestsDir}`);
    } catch (error) {
      // Handle Swagger generation errors gracefully
      console.warn(`⚠️  Warning: Could not generate Swagger tests: ${error.message}`);
    }
  }

  // Generate TypeScript configuration if TypeScript is enabled
  if (useTypeScript) {
    await fs.outputFile('tsconfig.json', JSON.stringify({
      "compilerOptions": {
        "target": "es2018",                    // Modern JavaScript target
        "lib": ["es2018", "dom"],              // Include DOM types for browser APIs
        "types": ["cypress", "node", "chai-json-schema"], // Include type definitions
        "moduleResolution": "node",            // Use Node.js module resolution
        "esModuleInterop": true,               // Enable ES module interoperability
        "allowSyntheticDefaultImports": true,  // Allow default imports from modules
        "strict": false,                       // Disable strict mode for easier Cypress usage
        "skipLibCheck": true,                  // Skip type checking of declaration files
        "forceConsistentCasingInFileNames": true, // Ensure consistent file naming
        "resolveJsonModule": true,             // Allow importing JSON files
        "allowJs": true,                       // Allow JavaScript files
        "noEmit": true                         // Don't emit compiled files
      },
      "include": [
        "cypress/**/*.ts",                     // Include all Cypress TypeScript files
        "cypress/**/*.js",                     // Include JavaScript files too
        "cypress.config.ts"                    // Include Cypress config file
      ],
      "exclude": [
        "node_modules",                        // Exclude dependencies
        "cypress/videos",                      // Exclude test artifacts
        "cypress/screenshots",
        "cypress/reports"
      ]
    }, null, 2));
    generated.push('tsconfig.json');
  }

  // Return the list of all generated files for summary reporting
  return generated;
};
