const axios = require('axios');
const fs = require('fs-extra');
const chalk = require('chalk');

/**
 * Fetches and parses Swagger/OpenAPI document
 */
async function fetchSwaggerDoc(swaggerUrl) {
  try {
    console.log(chalk.blue(`📡 Fetching Swagger document from: ${swaggerUrl}`));
    const response = await axios.get(swaggerUrl, {
      headers: {
        'Accept': 'application/json'
      },
      timeout: 10000
    });

    // Check if response is JSON
    if (typeof response.data === 'string') {
      try {
        return JSON.parse(response.data);
      } catch (parseError) {
        throw new Error('Response is not valid JSON. Make sure the URL points to a Swagger/OpenAPI JSON document.');
      }
    }

    return response.data;
  } catch (error) {
    if (error.response) {
      throw new Error(`Failed to fetch Swagger document (${error.response.status}): ${error.response.statusText}`);
    } else if (error.code === 'ENOTFOUND') {
      throw new Error(`Cannot reach the URL. Please check the URL and your internet connection.`);
    } else {
      throw new Error(`Failed to fetch Swagger document: ${error.message}`);
    }
  }
}

/**
 * Generates HTTP method name for test descriptions
 */
function getMethodName(method) {
  const methodNames = {
    'get': 'GET',
    'post': 'POST',
    'put': 'PUT',
    'patch': 'PATCH',
    'delete': 'DELETE',
    'head': 'HEAD',
    'options': 'OPTIONS'
  };
  return methodNames[method.toLowerCase()] || method.toUpperCase();
}

/**
 * Generates test data based on parameter schema
 */
function generateTestData(parameter) {
  const type = parameter.type || parameter.schema?.type;
  const format = parameter.format || parameter.schema?.format;
  
  switch (type) {
    case 'string':
      if (format === 'email') return '<EMAIL>';
      if (format === 'date') return '2023-01-01';
      if (format === 'date-time') return '2023-01-01T00:00:00Z';
      return parameter.example || 'test-string';
    case 'integer':
    case 'number':
      return parameter.example || 123;
    case 'boolean':
      return parameter.example || true;
    case 'array':
      return parameter.example || ['item1', 'item2'];
    case 'object':
      return parameter.example || { key: 'value' };
    default:
      return parameter.example || 'test-value';
  }
}

/**
 * Generates request body for POST/PUT/PATCH requests
 */
function generateRequestBody(requestBody) {
  if (!requestBody || !requestBody.content) return null;
  
  const contentType = Object.keys(requestBody.content)[0];
  const schema = requestBody.content[contentType]?.schema;
  
  if (!schema) return null;
  
  if (schema.example) return schema.example;
  
  // Generate basic request body based on schema
  if (schema.type === 'object' && schema.properties) {
    const body = {};
    Object.keys(schema.properties).forEach(prop => {
      const property = schema.properties[prop];
      body[prop] = generateTestData({ type: property.type, format: property.format, example: property.example });
    });
    return body;
  }
  
  return { data: 'test-data' };
}

/**
 * Gets expected status codes for an operation
 */
function getExpectedStatusCodes(responses) {
  const statusCodes = Object.keys(responses).filter(code => code !== 'default');
  return statusCodes.length > 0 ? statusCodes : ['200'];
}

/**
 * Generates a complete test file for a single API endpoint
 */
function generateSingleEndpointTest(path, method, operation, swaggerDoc, useTypeScript = false) {
  const ext = useTypeScript ? 'ts' : 'js';
  const methodName = getMethodName(method);
  const operationId = operation.operationId || `${method}${path.replace(/[^a-zA-Z0-9]/g, '')}`;
  const summary = operation.summary || `${methodName} ${path}`;
  const description = operation.description || summary;

  // Get base URL
  const baseUrl = swaggerDoc.host ?
    `${swaggerDoc.schemes?.[0] || 'https'}://${swaggerDoc.host}${swaggerDoc.basePath || ''}` :
    swaggerDoc.servers?.[0]?.url || '';

  // Get parameters
  const pathParams = (operation.parameters || []).filter(p => p.in === 'path');
  const queryParams = (operation.parameters || []).filter(p => p.in === 'query');
  const headerParams = (operation.parameters || []).filter(p => p.in === 'header');

  // Generate path with parameters
  let testPath = path;
  pathParams.forEach(param => {
    const testValue = generateTestData(param);
    testPath = testPath.replace(`{${param.name}}`, testValue);
  });

  // Generate query parameters
  const queryString = queryParams.length > 0
    ? '?' + queryParams.map(p => `${p.name}=${generateTestData(p)}`).join('&')
    : '';

  // Generate headers
  const headers = {};
  headerParams.forEach(param => {
    headers[param.name] = generateTestData(param);
  });

  // Generate request body for POST/PUT/PATCH
  const requestBody = ['post', 'put', 'patch'].includes(method.toLowerCase())
    ? generateRequestBody(operation.requestBody)
    : null;

  // Get expected status codes
  const expectedCodes = getExpectedStatusCodes(operation.responses);

  // Generate filename
  const sanitizedPath = path.replace(/[^a-zA-Z0-9]/g, '_').replace(/^_+|_+$/g, '');
  const filename = `${method.toLowerCase()}_${sanitizedPath}.spec.${ext}`;

  // Generate comprehensive test content in standard format
  const schemaImportPath = useTypeScript ? '../../../fixtures/api-schemas' : '../../../fixtures/api-schemas.js';
  let testContent = `import { getSchema, validateResponseSchema } from '${schemaImportPath}';

describe('${methodName} ${path} - ${summary}', () => {
  it('should make a successful ${method.toUpperCase()} request to ${path}', () => {
    cy.api({
      method: '${method.toUpperCase()}',
      url: '${baseUrl}${testPath}${queryString}',${Object.keys(headers).length > 0 ? `
      headers: ${JSON.stringify(headers, null, 6)},` : ''}${requestBody ? `
      body: ${JSON.stringify(requestBody, null, 6)},` : ''}
    }).then((response) => {
      expect(response.status).to.be.oneOf([${expectedCodes.join(', ')}]);
      expect(response.body).to.exist;${requestBody ? `
      // Add specific validations for POST/PUT/PATCH requests
      // expect(response.body).to.have.property('id');` : ''}
    });
  });

  it('should validate response schema for ${method.toUpperCase()} ${path}', () => {
    cy.api({
      method: '${method.toUpperCase()}',
      url: '${baseUrl}${testPath}${queryString}',${Object.keys(headers).length > 0 ? `
      headers: ${JSON.stringify(headers, null, 6)},` : ''}${requestBody ? `
      body: ${JSON.stringify(requestBody, null, 6)},` : ''}
      failOnStatusCode: false
    }).then((response) => {
      // Get schema for the response status code
      const schema = getSchema('${method.toUpperCase()}', '${path}', response.status);

      if (schema) {
        expect(response.body).to.be.jsonSchema(schema);
        cy.log('✅ Response schema validation passed');
      } else {
        cy.log('⚠️ No schema defined for this endpoint and status code');
      }
    });
  });
`;

  // Add invalid path parameters test if path parameters exist
  if (pathParams.length > 0) {
    testContent += `
  it('should handle invalid path parameters', () => {
    cy.api({
      method: '${method.toUpperCase()}',
      url: '${baseUrl}${path.replace(/{[^}]+}/g, 'invalid-id')}${queryString}',${Object.keys(headers).length > 0 ? `
      headers: ${JSON.stringify(headers, null, 6)},` : ''}${requestBody ? `
      body: ${JSON.stringify(requestBody, null, 6)},` : ''}
      failOnStatusCode: false
    }).then((response) => {
      expect(response.status).to.be.oneOf([400, 404]);
      expect(response.body).to.exist;
    });
  });
`;
  }

  // Add invalid request body test if request body exists
  if (requestBody) {
    testContent += `
  it('should handle invalid request body', () => {
    cy.api({
      method: '${method.toUpperCase()}',
      url: '${baseUrl}${testPath}${queryString}',${Object.keys(headers).length > 0 ? `
      headers: ${JSON.stringify(headers, null, 6)},` : ''}
      body: { invalid: 'data' },
      failOnStatusCode: false
    }).then((response) => {
      expect(response.status).to.be.oneOf([400, 422]);
      expect(response.body).to.exist;
    });
  });
`;
  }

  // Add error response tests for each error code
  const errorCodes = expectedCodes.filter(code => parseInt(code) >= 400);
  errorCodes.forEach(code => {
    testContent += `
  it('should return ${code} error when appropriate', () => {
    cy.api({
      method: '${method.toUpperCase()}',
      url: '${baseUrl}${testPath}${queryString}',${Object.keys(headers).length > 0 ? `
      headers: ${JSON.stringify(headers, null, 6)},` : ''}${requestBody ? `
      body: ${JSON.stringify(requestBody, null, 6)},` : ''}
      failOnStatusCode: false
    }).then((response) => {
      if (response.status === ${code}) {
        expect(response.status).to.eq(${code});
        expect(response.body).to.exist;

        // Validate error response schema
        const errorSchema = getSchema('${method.toUpperCase()}', '${path}', ${code});
        if (errorSchema) {
          expect(response.body).to.be.jsonSchema(errorSchema);
          cy.log('✅ Error response schema validation passed');
        }
      }
    });
  });
`;
  });

  // Add response time test
  testContent += `
  it('should respond within acceptable time limits', () => {
    const startTime = Date.now();

    cy.api({
      method: '${method.toUpperCase()}',
      url: '${baseUrl}${testPath}${queryString}',${Object.keys(headers).length > 0 ? `
      headers: ${JSON.stringify(headers, null, 6)},` : ''}${requestBody ? `
      body: ${JSON.stringify(requestBody, null, 6)},` : ''}
      failOnStatusCode: false
    }).then((response) => {
      const responseTime = Date.now() - startTime;
      expect(responseTime).to.be.lessThan(5000);
      expect(response.status).to.exist;
      expect(response.body).to.exist;
    });
  });
});`;

  return {
    filename,
    content: testContent,
    method: method.toUpperCase(),
    path,
    summary
  };
}

/**
 * Generates separate test files for each API endpoint
 */
function generateSwaggerTests(swaggerDoc, useTypeScript = false) {
  const ext = useTypeScript ? 'ts' : 'js';
  const info = swaggerDoc.info || {};
  const title = info.title || 'API';
  const version = info.version || '1.0.0';

  const testFiles = [];

  // Generate a test file for each endpoint
  Object.keys(swaggerDoc.paths || {}).forEach(path => {
    const pathItem = swaggerDoc.paths[path];

    Object.keys(pathItem).forEach(method => {
      if (['get', 'post', 'put', 'patch', 'delete', 'head', 'options'].includes(method)) {
        const operation = pathItem[method];
        const testFile = generateSingleEndpointTest(path, method, operation, swaggerDoc, useTypeScript);
        testFiles.push(testFile);
      }
    });
  });

  // Generate a master test suite file that imports all endpoint tests
  const masterTestContent = `describe('${title} API - Complete Test Suite', () => {
  it('should have consistent API documentation', () => {
    cy.log('📋 API Endpoints Summary:');
    // Total endpoints: ${testFiles.length}
    expect(${testFiles.length}).to.be.greaterThan(0, 'API should have at least one endpoint');
  });
});`;

  return {
    testFiles,
    masterFile: {
      filename: title.toLowerCase().replace(/[^a-z0-9]/g, '_') + '_api_suite.spec.' + ext,
      content: masterTestContent
    }
  };
}

/**
 * Main function to generate Swagger tests
 */
exports.generateSwaggerApiTests = async function(swaggerUrl, useTypeScript = false) {
  try {
    // Fetch Swagger document
    const swaggerDoc = await fetchSwaggerDoc(swaggerUrl);

    // Validate Swagger document
    if (!swaggerDoc || typeof swaggerDoc !== 'object') {
      throw new Error('Invalid document format. Expected JSON object.');
    }

    if (!swaggerDoc.paths && !swaggerDoc.openapi && !swaggerDoc.swagger) {
      throw new Error('Invalid Swagger/OpenAPI document. Missing required fields (paths, openapi, or swagger version).');
    }

    if (!swaggerDoc.paths || Object.keys(swaggerDoc.paths).length === 0) {
      throw new Error('No API paths found in the Swagger document.');
    }

    console.log(chalk.green('✅ Successfully parsed Swagger document'));
    console.log(chalk.blue('📊 Found ' + Object.keys(swaggerDoc.paths || {}).length + ' API paths'));

    // Count total endpoints
    let totalEndpoints = 0;
    Object.values(swaggerDoc.paths || {}).forEach(pathItem => {
      totalEndpoints += Object.keys(pathItem).filter(key =>
        ['get', 'post', 'put', 'patch', 'delete', 'head', 'options'].includes(key)
      ).length;
    });

    console.log(chalk.blue('🔗 Total API endpoints: ' + totalEndpoints));

    // Generate test files
    const { testFiles, masterFile } = generateSwaggerTests(swaggerDoc, useTypeScript);

    // Create swagger-tests subdirectory
    const swaggerTestsDir = 'cypress/e2e/api/swagger-tests';
    await fs.ensureDir(swaggerTestsDir);

    // Save individual endpoint test files
    const savedFiles = [];
    for (const testFile of testFiles) {
      const filePath = swaggerTestsDir + '/' + testFile.filename;
      await fs.outputFile(filePath, testFile.content);
      savedFiles.push(filePath);
      console.log(chalk.green('📝 Generated: ' + testFile.method + ' ' + testFile.path + ' -> ' + testFile.filename));
    }

    // Save master test suite file
    const masterFilePath = 'cypress/e2e/api/' + masterFile.filename;
    await fs.outputFile(masterFilePath, masterFile.content);
    savedFiles.push(masterFilePath);

    console.log(chalk.green('📋 Generated master test suite: ' + masterFile.filename));
    console.log(chalk.cyan('📁 All test files saved in: ' + swaggerTestsDir + '/'));
    console.log(chalk.yellow('💡 Individual endpoint tests: ' + testFiles.length + ' files'));
    console.log(chalk.yellow('💡 Master test suite: 1 file'));

    return {
      filePaths: savedFiles,
      endpointCount: totalEndpoints,
      pathCount: Object.keys(swaggerDoc.paths || {}).length,
      testFileCount: testFiles.length + 1,
      swaggerTestsDir
    };

  } catch (error) {
    console.error(chalk.red('❌ Error generating Swagger tests: ' + error.message));
    throw error;
  }
};
