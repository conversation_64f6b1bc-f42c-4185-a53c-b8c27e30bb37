/**
 * OpenAI Test Generation Module
 *
 * This module handles OpenAI API interactions to generate AI-powered API tests
 * for Cypress. It creates comprehensive test suites for common REST endpoints
 * with proper assertions, schema validation, and error handling.
 *
 * Features:
 * - Generates tests for GET, POST, PUT, DELETE endpoints
 * - Supports both JavaScript and TypeScript output
 * - Includes proper error handling and edge cases
 * - Uses cy.api() command from cypress-plugin-api
 * - Implements schema validation with chai-json-schema
 */

const OpenAI = require('openai');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const axios = require('axios');

/**
 * Generates AI-powered API tests using OpenAI
 *
 * @param {Object} config - Configuration object
 * @param {string} config.apiKey - OpenAI API key
 * @param {string} config.model - OpenAI model to use
 * @param {string} config.swaggerUrl - Swagger/OpenAPI JSON URL
 * @param {boolean} config.useTypeScript - Whether to generate TypeScript tests
 * @returns {Promise<Object>} Generation result with file paths and statistics
 */
exports.generateOpenAIApiTests = async function(config) {
  const { apiKey, model, swaggerUrl, useTypeScript } = config;
  const ext = useTypeScript ? 'ts' : 'js';
  const generated = [];

  // Initialize OpenAI client
  const openai = new OpenAI({
    apiKey: apiKey,
  });

  // Create output directory structure
  const outputDir = 'cypress/e2e/api/openai-automation';
  await fs.ensureDir(outputDir);

  // Create subdirectories for better organization
  await fs.ensureDir(path.join(outputDir, 'generated'));
  await fs.ensureDir(path.join(outputDir, 'fallback'));

  let totalEndpoints = 0;
  let successfulGenerations = 0;

  console.log(chalk.cyan('\n🤖 Fetching API specification from Swagger/OpenAPI...'));

  try {
    // Fetch and parse Swagger/OpenAPI specification
    const swaggerSpec = await fetchSwaggerSpec(swaggerUrl);
    const endpoints = extractEndpointsFromSwagger(swaggerSpec);

    console.log(chalk.green(`✅ Found ${endpoints.length} API endpoints to generate tests for`));
    console.log(chalk.cyan('🤖 Generating AI-powered API tests...'));

    for (const endpointInfo of endpoints) {
      totalEndpoints++;

      try {
        const testCode = await generateTestForEndpoint(openai, {
          method: endpointInfo.method,
          endpoint: endpointInfo.path,
          baseUrl: swaggerSpec.host ? `${swaggerSpec.schemes?.[0] || 'https'}://${swaggerSpec.host}${swaggerSpec.basePath || ''}` : swaggerUrl.split('/swagger')[0],
          model,
          useTypeScript,
          operationInfo: endpointInfo.operation
        });

        if (testCode) {
          // Create filename based on method and endpoint
          const sanitizedEndpoint = endpointInfo.path.replace(/[^a-zA-Z0-9]/g, '_').replace(/^_+|_+$/g, '');
          const filename = `${endpointInfo.method.toLowerCase()}_${sanitizedEndpoint}.spec.${ext}`;
          const filePath = path.join(outputDir, 'generated', filename);

          await fs.outputFile(filePath, testCode);
          generated.push(filePath);
          successfulGenerations++;

          console.log(chalk.green(`  ✅ Generated ${endpointInfo.method} test for ${endpointInfo.path}`));
        }
      } catch (error) {
        console.log(chalk.yellow(`  ⚠️  OpenAI failed for ${endpointInfo.method} ${endpointInfo.path}: ${error.message}`));

        // Generate fallback spec when OpenAI fails
        try {
          const fallbackCode = generateFallbackSpec(endpointInfo, swaggerSpec, useTypeScript);
          const sanitizedEndpoint = endpointInfo.path.replace(/[^a-zA-Z0-9]/g, '_').replace(/^_+|_+$/g, '');
          const filename = `${endpointInfo.method.toLowerCase()}_${sanitizedEndpoint}.spec.${ext}`;
          const filePath = path.join(outputDir, 'fallback', filename);

          await fs.outputFile(filePath, fallbackCode);
          generated.push(filePath);

          console.log(chalk.blue(`  📝 Created fallback spec for ${endpointInfo.method} ${endpointInfo.path}`));
        } catch (fallbackError) {
          console.log(chalk.red(`  ❌ Failed to create fallback spec: ${fallbackError.message}`));
        }
      }
    }
  } catch (error) {
    console.log(chalk.red(`  ❌ Failed to fetch or parse Swagger specification: ${error.message}`));
    throw error;
  }

  return {
    filePaths: generated,
    testFileCount: generated.length,
    endpointCount: totalEndpoints,
    successfulGenerations,
    outputDir
  };
};

/**
 * Fetches Swagger/OpenAPI specification from URL
 *
 * @param {string} swaggerUrl - URL to Swagger/OpenAPI JSON
 * @returns {Promise<Object>} Parsed Swagger specification
 */
async function fetchSwaggerSpec(swaggerUrl) {
  try {
    console.log(chalk.gray(`  📡 Fetching: ${swaggerUrl}`));
    const response = await axios.get(swaggerUrl, {
      timeout: 10000,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Cypress-Bootstrapper-AI/1.0'
      }
    });

    if (typeof response.data === 'string') {
      return JSON.parse(response.data);
    }

    return response.data;
  } catch (error) {
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      throw new Error(`Cannot reach Swagger URL: ${swaggerUrl}. Please check the URL and your internet connection.`);
    } else if (error.response?.status === 404) {
      throw new Error(`Swagger specification not found at: ${swaggerUrl}`);
    } else if (error.response?.status >= 400) {
      throw new Error(`HTTP ${error.response.status} error fetching Swagger specification`);
    } else {
      throw new Error(`Failed to fetch Swagger specification: ${error.message}`);
    }
  }
}

/**
 * Extracts API endpoints from Swagger/OpenAPI specification
 *
 * @param {Object} swaggerSpec - Parsed Swagger specification
 * @returns {Array<Object>} Array of endpoint information
 */
function extractEndpointsFromSwagger(swaggerSpec) {
  const endpoints = [];

  if (!swaggerSpec.paths) {
    throw new Error('Invalid Swagger specification: missing paths');
  }

  for (const [path, pathItem] of Object.entries(swaggerSpec.paths)) {
    for (const [method, operation] of Object.entries(pathItem)) {
      if (['get', 'post', 'put', 'delete', 'patch'].includes(method.toLowerCase())) {
        endpoints.push({
          path,
          method: method.toUpperCase(),
          operation
        });
      }
    }
  }

  return endpoints;
}

/**
 * Generates a test file for a specific endpoint and HTTP method
 *
 * @param {OpenAI} openai - OpenAI client instance
 * @param {Object} config - Test generation configuration
 * @returns {Promise<string>} Generated test code
 */
async function generateTestForEndpoint(openai, config) {
  const { method, endpoint, baseUrl, model, useTypeScript, operationInfo } = config;

  const systemPrompt = `You are an expert Cypress test automation engineer specializing in API testing. Generate comprehensive, production-ready Cypress API tests using the cy.api() command from cypress-plugin-api.

Requirements:
- Use cy.api() command for all HTTP requests
- Include proper assertions for status codes, response structure, and data validation
- Add schema validation using chai-json-schema where appropriate
- Handle both success and error scenarios
- Include descriptive test names and comments
- Use proper ${useTypeScript ? 'TypeScript' : 'JavaScript'} syntax
- Follow Cypress best practices
- Include realistic test data and edge cases
- Add proper error handling and negative test cases

Generate ONLY the test code without any explanations or markdown formatting.`;

  // Build detailed operation information from Swagger spec
  const operationDetails = operationInfo ? `
- Operation Summary: ${operationInfo.summary || 'N/A'}
- Description: ${operationInfo.description || 'N/A'}
- Parameters: ${JSON.stringify(operationInfo.parameters || [], null, 2)}
- Request Body: ${JSON.stringify(operationInfo.requestBody || {}, null, 2)}
- Responses: ${JSON.stringify(operationInfo.responses || {}, null, 2)}
- Tags: ${operationInfo.tags?.join(', ') || 'N/A'}` : '';

  const userPrompt = `Generate a comprehensive Cypress API test suite for:
- HTTP Method: ${method}
- Endpoint: ${endpoint}
- Base URL: ${baseUrl}
- Language: ${useTypeScript ? 'TypeScript' : 'JavaScript'}
${operationDetails}

Include tests for:
1. Successful ${method} request with proper assertions based on the expected response schema
2. Error handling (404, 400, 500 scenarios) as defined in the API specification
3. Schema validation for response structure using the provided response schemas
4. Parameter validation and edge cases based on the parameter definitions
5. Authentication scenarios if applicable
6. Request body validation for POST/PUT requests

Use realistic test data that matches the parameter types and constraints.
The test should be complete and ready to run.`;

  try {
    const completion = await openai.chat.completions.create({
      model: model,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      max_tokens: 2000,
      temperature: 0.3,
    });

    const generatedCode = completion.choices[0]?.message?.content;
    
    if (!generatedCode) {
      throw new Error('No code generated from OpenAI');
    }

    // Extract and clean the code
    return extractAndCleanCode(generatedCode, useTypeScript);
  } catch (error) {
    throw new Error(`OpenAI API error: ${error.message}`);
  }
}

/**
 * Extracts and cleans code from OpenAI response
 *
 * @param {string} rawCode - Raw code from OpenAI
 * @param {boolean} useTypeScript - Whether the code should be TypeScript
 * @returns {string} Cleaned and formatted code
 */
function extractAndCleanCode(rawCode, useTypeScript) {
  // Remove markdown code blocks if present
  let cleanCode = rawCode.replace(/```(?:javascript|typescript|js|ts)?\n?/g, '').replace(/```/g, '');
  
  // Ensure proper imports for TypeScript
  if (useTypeScript && !cleanCode.includes('import')) {
    const imports = `/**
 * AI-Generated API Test
 * Generated by Cypress Bootstrapper AI with OpenAI
 */

`;
    cleanCode = imports + cleanCode;
  } else if (!useTypeScript && !cleanCode.includes('/**')) {
    const header = `/**
 * AI-Generated API Test
 * Generated by Cypress Bootstrapper AI with OpenAI
 */

`;
    cleanCode = header + cleanCode;
  }

  return cleanCode.trim();
}

/**
 * Generates a fallback test spec when OpenAI generation fails
 *
 * @param {Object} endpointInfo - Endpoint information from Swagger
 * @param {Object} swaggerSpec - Full Swagger specification
 * @param {boolean} useTypeScript - Whether to generate TypeScript
 * @returns {string} Fallback test code
 */
function generateFallbackSpec(endpointInfo, swaggerSpec, useTypeScript) {
  const { method, path, operation } = endpointInfo;
  const baseUrl = swaggerSpec.host ?
    `${swaggerSpec.schemes?.[0] || 'https'}://${swaggerSpec.host}${swaggerSpec.basePath || ''}` :
    'https://api.example.com';

  // Extract parameters from operation
  const parameters = operation.parameters || [];
  const pathParams = parameters.filter(p => p.in === 'path');
  const queryParams = parameters.filter(p => p.in === 'query');
  const bodyParam = parameters.find(p => p.in === 'body');

  // Extract response codes
  const responses = Object.keys(operation.responses || {});
  const successCode = responses.find(code => code.startsWith('2')) || '200';
  const errorCodes = responses.filter(code => !code.startsWith('2'));

  // Build test path with parameters
  let testPath = path;
  pathParams.forEach(param => {
    testPath = testPath.replace(`{${param.name}}`, `\${${param.name}}`);
  });

  const testContent = `/**
 * Fallback API Test Spec
 * Generated by Cypress Bootstrapper AI
 *
 * Endpoint: ${method} ${path}
 * Description: ${operation.summary || 'No description available'}
 */

describe('${method} ${path}', () => {
  const baseUrl = '${baseUrl}';

  beforeEach(() => {
    // Setup test data and authentication if needed
    // cy.login(); // Uncomment if authentication is required
  });

  it('should successfully ${method.toLowerCase()} ${path}', () => {
    ${generateSuccessTest(method, testPath, pathParams, queryParams, bodyParam, successCode)}
  });

  ${errorCodes.map(code => generateErrorTest(method, testPath, code)).join('\n\n  ')}

  ${generateParameterTests(method, testPath, pathParams, queryParams)}

  ${bodyParam ? generateBodyValidationTest(method, testPath, bodyParam) : ''}
});
`;

  return testContent;
}

/**
 * Generates success test case
 */
function generateSuccessTest(method, testPath, pathParams, queryParams, bodyParam, successCode) {
  let testCode = '';

  // Set up path parameters
  if (pathParams.length > 0) {
    pathParams.forEach(param => {
      const sampleValue = getSampleValue(param.type, param.name);
      testCode += `    const ${param.name} = ${sampleValue};\n`;
    });
  }

  // Build request object
  testCode += `    cy.api({\n`;
  testCode += `      method: '${method}',\n`;
  testCode += `      url: \`\${baseUrl}${testPath}\`,\n`;

  // Add query parameters if any
  if (queryParams.length > 0) {
    testCode += `      qs: {\n`;
    queryParams.forEach(param => {
      const sampleValue = getSampleValue(param.type, param.name);
      testCode += `        ${param.name}: ${sampleValue},\n`;
    });
    testCode += `      },\n`;
  }

  // Add body for POST/PUT requests
  if (bodyParam && ['POST', 'PUT', 'PATCH'].includes(method)) {
    testCode += `      body: {\n`;
    testCode += `        // Add request body based on your API schema\n`;
    testCode += `        // Refer to Swagger spec for exact structure\n`;
    testCode += `      },\n`;
  }

  testCode += `    }).then((response) => {\n`;
  testCode += `      expect(response.status).to.eq(${successCode});\n`;
  testCode += `      expect(response.body).to.exist;\n`;
  testCode += `      // Add more specific assertions based on your API response\n`;
  testCode += `    });\n`;

  return testCode;
}

/**
 * Generates error test case
 */
function generateErrorTest(method, testPath, errorCode) {
  const errorScenario = getErrorScenario(errorCode);

  return `it('should handle ${errorCode} error - ${errorScenario.description}', () => {
    cy.api({
      method: '${method}',
      url: \`\${baseUrl}${errorScenario.path || testPath}\`,
      failOnStatusCode: false
    }).then((response) => {
      expect(response.status).to.eq(${errorCode});
      ${errorScenario.assertions}
    });
  });`;
}

/**
 * Generates parameter validation tests
 */
function generateParameterTests(method, testPath, pathParams, queryParams) {
  if (pathParams.length === 0 && queryParams.length === 0) return '';

  return `it('should validate required parameters', () => {
    // Test with missing required parameters
    cy.api({
      method: '${method}',
      url: \`\${baseUrl}${testPath.replace(/\$\{[^}]+\}/g, 'invalid')}\`,
      failOnStatusCode: false
    }).then((response) => {
      expect(response.status).to.be.oneOf([400, 404, 422]);
    });
  });`;
}

/**
 * Generates body validation test for POST/PUT requests
 */
function generateBodyValidationTest(method, testPath, bodyParam) {
  return `it('should validate request body', () => {
    cy.api({
      method: '${method}',
      url: \`\${baseUrl}${testPath}\`,
      body: {}, // Empty body to test validation
      failOnStatusCode: false
    }).then((response) => {
      expect(response.status).to.be.oneOf([400, 422]);
      expect(response.body).to.have.property('error');
    });
  });`;
}

/**
 * Gets sample value based on parameter type
 */
function getSampleValue(type, name) {
  switch (type) {
    case 'integer':
      return name.toLowerCase().includes('id') ? '1' : '123';
    case 'string':
      return name.toLowerCase().includes('email') ? "'<EMAIL>'" : "'test-value'";
    case 'boolean':
      return 'true';
    case 'array':
      return "['item1', 'item2']";
    default:
      return "'sample-value'";
  }
}

/**
 * Gets error scenario details
 */
function getErrorScenario(errorCode) {
  const scenarios = {
    '400': {
      description: 'Bad Request',
      path: null,
      assertions: "expect(response.body).to.have.property('error');"
    },
    '401': {
      description: 'Unauthorized',
      path: null,
      assertions: "expect(response.body).to.have.property('message');"
    },
    '403': {
      description: 'Forbidden',
      path: null,
      assertions: "expect(response.body).to.have.property('error');"
    },
    '404': {
      description: 'Not Found',
      path: '/nonexistent-resource',
      assertions: "expect(response.body).to.have.property('error');"
    },
    '422': {
      description: 'Validation Error',
      path: null,
      assertions: "expect(response.body).to.have.property('errors');"
    },
    '500': {
      description: 'Internal Server Error',
      path: null,
      assertions: "expect(response.body).to.have.property('error');"
    }
  };

  return scenarios[errorCode] || {
    description: 'Error',
    path: null,
    assertions: "expect(response.body).to.exist;"
  };
}

/**
 * Validates OpenAI configuration and checks API quota/balance
 *
 * @param {Object} config - Configuration to validate
 * @throws {Error} If configuration is invalid or quota exceeded
 */
exports.validateOpenAIConfig = async function(config) {
  const { apiKey, model, swaggerUrl } = config;

  if (!apiKey || !apiKey.startsWith('sk-')) {
    throw new Error('Invalid OpenAI API key. Key should start with "sk-"');
  }

  if (!model || !['gpt-4o', 'gpt-4o-mini', 'gpt-3.5-turbo'].includes(model)) {
    throw new Error('Invalid OpenAI model. Supported models: gpt-4o, gpt-4o-mini, gpt-3.5-turbo');
  }

  if (!swaggerUrl) {
    throw new Error('Swagger/OpenAPI URL is required for test generation');
  }

  try {
    new URL(swaggerUrl);
  } catch (error) {
    throw new Error('Invalid Swagger URL format');
  }

  // Check OpenAI API quota and balance
  await checkOpenAIQuota(apiKey, model);
};

/**
 * Checks OpenAI API quota and balance by making a test request
 *
 * @param {string} apiKey - OpenAI API key
 * @param {string} model - OpenAI model to test
 * @throws {Error} If quota exceeded or insufficient balance
 */
async function checkOpenAIQuota(apiKey, model) {
  console.log(chalk.gray('  🔍 Checking OpenAI API quota and balance...'));

  try {
    const openai = new OpenAI({ apiKey });

    // Make a minimal test request to check quota/balance
    const testCompletion = await openai.chat.completions.create({
      model: model,
      messages: [
        { role: 'system', content: 'You are a helpful assistant.' },
        { role: 'user', content: 'Say "test" if you can respond.' }
      ],
      max_tokens: 5,
      temperature: 0
    });

    if (testCompletion.choices && testCompletion.choices.length > 0) {
      console.log(chalk.green('  ✅ OpenAI API quota and balance verified'));
      return true;
    } else {
      throw new Error('Unexpected response from OpenAI API');
    }
  } catch (error) {
    // Handle specific OpenAI API errors
    if (error.status === 429) {
      throw new Error('OpenAI API quota exceeded. Please check your usage limits or upgrade your plan.');
    } else if (error.status === 401) {
      throw new Error('OpenAI API key is invalid or expired. Please check your API key.');
    } else if (error.status === 402) {
      throw new Error('OpenAI account has insufficient balance. Please add credits to your account.');
    } else if (error.status === 403) {
      throw new Error('OpenAI API access forbidden. Your account may be suspended or the model is not available.');
    } else if (error.code === 'insufficient_quota') {
      throw new Error('OpenAI API quota exceeded. Please check your usage limits or wait for quota reset.');
    } else if (error.code === 'billing_not_active') {
      throw new Error('OpenAI billing is not active. Please set up billing in your OpenAI account.');
    } else if (error.message && error.message.includes('quota')) {
      throw new Error('OpenAI API quota exceeded. Please check your usage limits.');
    } else if (error.message && error.message.includes('balance')) {
      throw new Error('Insufficient OpenAI account balance. Please add credits to your account.');
    } else {
      throw new Error(`OpenAI API error: ${error.message || 'Unknown error occurred'}`);
    }
  }
}

/**
 * Creates or updates cypress.env.json with OpenAI configuration
 *
 * @param {string} apiKey - OpenAI API key
 * @param {string} model - OpenAI model
 * @param {string} swaggerUrl - Swagger/OpenAPI URL
 * @returns {Promise<void>}
 */
exports.createCypressEnvFile = async function(apiKey, model, swaggerUrl) {
  const envFilePath = 'cypress.env.json';
  let envConfig = {};

  // Read existing env file if it exists
  if (await fs.pathExists(envFilePath)) {
    try {
      const existingContent = await fs.readFile(envFilePath, 'utf8');
      envConfig = JSON.parse(existingContent);
    } catch (error) {
      console.log(chalk.yellow('⚠️  Warning: Could not parse existing cypress.env.json, creating new file'));
    }
  }

  // Add OpenAI configuration
  envConfig.OPENAI_API_KEY = apiKey;
  envConfig.OPENAI_MODEL = model;
  envConfig.SWAGGER_URL = swaggerUrl;

  // Write the updated configuration
  await fs.outputFile(envFilePath, JSON.stringify(envConfig, null, 2));

  console.log(chalk.green('✅ Created/updated cypress.env.json with OpenAI configuration'));
};

/**
 * Updates .gitignore to exclude sensitive files
 *
 * @returns {Promise<void>}
 */
exports.updateGitignore = async function() {
  const gitignorePath = '.gitignore';
  const entriesToAdd = [
    'cypress.env.json',
    'cypress/videos/',
    'cypress/screenshots/',
    'cypress/reports/',
    'node_modules/',
    '.env',
    '.env.local'
  ];

  let gitignoreContent = '';

  // Read existing .gitignore if it exists
  if (await fs.pathExists(gitignorePath)) {
    gitignoreContent = await fs.readFile(gitignorePath, 'utf8');
  }

  // Add entries that don't already exist
  const linesToAdd = [];
  for (const entry of entriesToAdd) {
    if (!gitignoreContent.includes(entry)) {
      linesToAdd.push(entry);
    }
  }

  if (linesToAdd.length > 0) {
    // Add a section header if we're adding new entries
    if (gitignoreContent && !gitignoreContent.endsWith('\n')) {
      gitignoreContent += '\n';
    }

    gitignoreContent += '\n# Cypress Bootstrapper - Security and Test Artifacts\n';
    gitignoreContent += linesToAdd.join('\n') + '\n';

    await fs.outputFile(gitignorePath, gitignoreContent);
    console.log(chalk.green('✅ Updated .gitignore with security exclusions'));
  } else {
    console.log(chalk.gray('ℹ️  .gitignore already contains necessary exclusions'));
  }
};
