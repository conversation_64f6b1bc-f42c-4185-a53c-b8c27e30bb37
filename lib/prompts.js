/**
 * User Prompts Module
 *
 * This module handles all user interaction through command-line prompts.
 * It collects user preferences for project configuration including:
 * - Application base URL
 * - Git repository initialization
 * - CI/CD setup preferences
 * - API test generation from Swagger/OpenAPI
 * - Browser selection for testing
 * - TypeScript usage preference
 *
 * The prompts are designed to be user-friendly with clear descriptions
 * and sensible defaults to streamline the setup process.
 */

const inquirer = require('inquirer');  // Interactive command line prompts
const chalk = require('chalk');        // Terminal string styling for better UX

/**
 * Collects user preferences through interactive prompts
 *
 * This function presents a series of questions to the user in a specific order
 * to gather all necessary configuration options for the Cypress project setup.
 *
 * Prompt Order (as requested by user):
 * 1. Application base URL
 * 2. Git repository initialization
 * 3. GitHub Actions CI setup
 * 4. Swagger/OpenAPI test generation
 * 5. Browser selection
 * 6. TypeScript usage
 *
 * @returns {Promise<Object>} User choices object containing all configuration options
 */
exports.promptUser = async function() {
  // Main configuration prompts
  const answers = await inquirer.prompt([
    {
      type: 'input',
      name: 'baseUrl',
      message: 'Enter your application base URL:',
      default: 'http://localhost:3000',
      validate: function(input) {
        // Basic URL validation
        try {
          new URL(input);
          return true;
        } catch (error) {
          return 'Please enter a valid URL (e.g., http://localhost:3000)';
        }
      }
    },
    {
      type: 'confirm',
      name: 'gitInit',
      message: 'Choose whether to initialize a Git repository:',
      default: true
    },
    {
      type: 'confirm',
      name: 'includeGitHubCI',
      message: 'Want GitHub Actions CI setup:',
      default: true
      // Note: This prompt appears regardless of Git initialization choice
      // as per user requirements - allows CI setup for existing repos
    },
    {
      type: 'confirm',
      name: 'generateSwaggerTests',
      message: 'Decide if you want to generate API tests from Swagger/OpenAPI:',
      default: false
    },
    {
      type: 'list',
      name: 'browser',
      message: 'Select your preferred browser (chrome, firefox, edge, electron):',
      choices: [
        { name: 'Chrome (Recommended)', value: 'chrome' },
        { name: 'Firefox', value: 'firefox' },
        { name: 'Edge', value: 'edge' },
        { name: 'Electron (Headless)', value: 'electron' }
      ],
      default: 'chrome'
    },
    {
      type: 'confirm',
      name: 'useTypeScript',
      message: 'Choose whether to use TypeScript:',
      default: false
    }
  ]);

  // Conditional prompt for Swagger/OpenAPI URL if user wants API test generation
  if (answers.generateSwaggerTests) {
    // Display helpful examples of common Swagger/OpenAPI URLs
    console.log(chalk.cyan('\n📋 Common Swagger/OpenAPI JSON URLs:'));
    console.log(chalk.gray('  • Petstore Demo: https://petstore.swagger.io/v2/swagger.json'));
    console.log(chalk.gray('  • JSONPlaceholder: https://jsonplaceholder.typicode.com/swagger.json'));
    console.log(chalk.gray('  • Your API docs: https://your-api.com/swagger.json'));
    console.log(chalk.gray('  • Note: URL must return JSON, not HTML documentation\n'));

    // Prompt for the specific Swagger/OpenAPI JSON URL
    const swaggerPrompt = await inquirer.prompt([
      {
        type: 'input',
        name: 'swaggerUrl',
        message: 'Enter the Swagger/OpenAPI JSON URL:',
        default: 'https://petstore.swagger.io/v2/swagger.json',
        validate: function(input) {
          // Validate that a URL is provided
          if (!input.trim()) {
            return 'Please enter a valid Swagger URL';
          }

          try {
            // Validate URL format
            const url = new URL(input);

            // Check if URL likely points to a JSON document
            // This helps prevent users from entering HTML documentation URLs
            if (!input.includes('.json') && !input.includes('swagger') && !input.includes('openapi')) {
              return 'Please enter a URL that points to a Swagger/OpenAPI JSON document (should end with .json or contain swagger/openapi)';
            }

            return true;
          } catch (error) {
            return 'Please enter a valid URL (e.g., https://api.example.com/swagger.json)';
          }
        }
      }
    ]);

    // Add the Swagger URL to the main answers object
    answers.swaggerUrl = swaggerPrompt.swaggerUrl;
  }

  return answers;
};
