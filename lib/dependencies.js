/**
 * Dependency Management Module
 *
 * This module handles the installation of all required NPM packages for the
 * Cypress testing framework. It manages both base dependencies and optional
 * TypeScript-specific packages based on user preferences.
 *
 * Dependencies Installed:
 *
 * Base Dependencies (always installed):
 * - cypress                      → Core Cypress testing framework
 * - cypress-plugin-api           → Enhanced API testing capabilities
 * - cypress-xpath                → XPath selector support for complex DOM queries
 * - cypress-mochawesome-reporter → Advanced HTML test reporting
 * - faker                        → Test data generation library
 * - chai-json-schema             → JSON schema validation for API responses
 *
 * TypeScript Dependencies (optional):
 * - typescript                   → TypeScript compiler
 * - ts-node                      → TypeScript execution engine for Node.js
 * - @types/node                  → Node.js type definitions
 * - @types/fs-extra              → fs-extra type definitions
 * - @types/inquirer              → inquirer type definitions
 * - @types/cypress               → Cypress type definitions
 */

const execa = require('execa');  // Better child process execution with enhanced error handling

/**
 * Installs all required dependencies for the Cypress project
 *
 * This function handles the complete dependency installation process,
 * including both base Cypress dependencies and optional TypeScript
 * packages. All packages are installed as dev dependencies since
 * this is a testing framework setup.
 *
 * @param {boolean} useTypeScript - Whether to install TypeScript dependencies
 * @returns {Promise<boolean>} Success status of the installation process
 */
exports.installDependencies = async function(useTypeScript = false) {
  try {
    // Define base dependencies required for all Cypress projects
    const baseDeps = [
      'cypress',                      // Core Cypress testing framework
      'cypress-plugin-api',           // API testing plugin with enhanced features
      'cypress-xpath',                // XPath selector support for complex DOM queries
      'cypress-mochawesome-reporter', // Enhanced HTML reporting with screenshots
      'faker',                        // Test data generation for realistic testing
      'chai-json-schema'              // JSON schema validation for API response testing
    ];

    // Install base dependencies
    console.log('Installing base dependencies...');
    await execa('npm', ['install', ...baseDeps, '--save-dev'], {
      stdio: 'inherit'  // Show npm output directly to user
    });

    // Install TypeScript dependencies if requested
    if (useTypeScript) {
      console.log('Installing TypeScript dependencies...');
      const tsDeps = [
        'typescript',        // TypeScript compiler
        'ts-node',          // TypeScript execution engine
        '@types/node',      // Node.js type definitions
        '@types/fs-extra',  // fs-extra type definitions
        '@types/inquirer',  // inquirer type definitions
        '@types/cypress'    // Cypress type definitions
      ];

      await execa('npm', ['install', ...tsDeps, '--save-dev'], {
        stdio: 'inherit'  // Show npm output directly to user
      });
    }

    return true;  // Installation successful
  } catch (error) {
    // Handle installation errors gracefully
    console.error('Failed to install dependencies:', error.message);
    return false;  // Installation failed
  }
};
