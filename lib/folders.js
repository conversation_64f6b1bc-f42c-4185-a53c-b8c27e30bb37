/**
 * Project Folder Structure Module
 *
 * This module is responsible for creating the complete folder structure
 * for a Cypress testing project. It establishes a well-organized directory
 * layout that follows Cypress best practices and supports both UI and API testing.
 *
 * Folder Structure Created:
 * - cypress/e2e/api/          → API test files
 * - cypress/e2e/ui/pages/     → Page Object Model files
 * - cypress/e2e/ui/tests/     → UI test files
 * - cypress/fixtures/         → Test data and schema files
 * - cypress/reports/          → Test execution reports
 * - cypress/support/          → Support files and custom commands
 * - .github/workflows/        → GitHub Actions CI/CD workflows
 */

const fs = require('fs-extra');  // Enhanced file system operations with extra utilities
const path = require('path');    // Node.js path manipulation utilities

/**
 * Creates the complete folder structure for a Cypress testing project
 *
 * This function creates all necessary directories for organizing tests,
 * page objects, fixtures, reports, and CI/CD configurations. The structure
 * is designed to scale with project growth and maintain clear separation
 * of concerns between different types of tests and assets.
 *
 * @returns {Array<string>} Array of created folder paths for tracking purposes
 */
exports.createFolders = function() {
  const baseDir = process.cwd();  // Get current working directory

  // Define the complete folder structure
  const folders = [
    'cypress/e2e/api',           // API test files (REST, GraphQL, etc.)
    'cypress/e2e/ui/pages',      // Page Object Model classes
    'cypress/e2e/ui/tests',      // UI/E2E test files
    'cypress/fixtures',          // Test data, JSON schemas, mock responses
    'cypress/reports',           // Test execution reports and artifacts
    'cypress/support',           // Custom commands, utilities, and global setup
    '.github/workflows'          // GitHub Actions CI/CD workflow definitions
  ];

  // Create each folder synchronously to ensure proper structure
  folders.forEach(folder => {
    const fullPath = path.join(baseDir, folder);
    fs.ensureDirSync(fullPath);  // Creates directory and any necessary parent directories
  });

  // Return the list of created folders for summary reporting
  return folders;
};