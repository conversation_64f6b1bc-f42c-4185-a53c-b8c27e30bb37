name: Cypress Tests

# Trigger workflow on pushes and pull requests to main branches
on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  cypress-run:
    runs-on: ubuntu-latest

    steps:
      # Checkout the repository code
      - name: Checkout
        uses: actions/checkout@v3

      # Setup Node.js environment
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'

      # Install project dependencies
      - name: Install dependencies
        run: npm ci

      # Run Cypress tests
      - name: Cypress run
        uses: cypress-io/github-action@v5
        with:
          browser: chrome
          headless: true

      # Upload screenshots on test failure
      - name: Upload screenshots
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: cypress-screenshots
          path: cypress/screenshots
          retention-days: 7

      # Upload videos for all test runs
      - name: Upload videos
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: cypress-videos
          path: cypress/videos
          retention-days: 7

      # Upload test reports
      - name: Upload test reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: cypress-reports
          path: cypress/reports
          retention-days: 30